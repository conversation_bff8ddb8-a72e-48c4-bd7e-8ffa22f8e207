//+------------------------------------------------------------------+
//|                                           HFT_RiskManager.mqh |
//|                                    Professional Risk Management |
//+------------------------------------------------------------------+

#include <Trade\AccountInfo.mqh>
#include <Trade\PositionInfo.mqh>

//+------------------------------------------------------------------+
//| Risk Management Class                                            |
//+------------------------------------------------------------------+
class CHFTRiskManager
{
private:
    // Risk parameters
    double            m_riskPercent;
    double            m_maxDailyLoss;
    double            m_maxDrawdown;
    int               m_maxPositions;
    double            m_maxLotSize;
    
    // Account tracking
    double            m_startBalance;
    double            m_dayStartBalance;
    double            m_peakBalance;
    double            m_currentDrawdown;
    double            m_dailyPnL;
    
    // Position tracking
    int               m_currentPositions;
    double            m_totalExposure;
    
    // Risk flags
    bool              m_riskLimitExceeded;
    bool              m_dailyLimitExceeded;
    bool              m_drawdownLimitExceeded;
    
    CAccountInfo      account;
    CPositionInfo     position;

public:
    // Constructor/Destructor
                     CHFTRiskManager();
                    ~CHFTRiskManager();
    
    // Initialization
    bool              Initialize(double riskPercent, double maxDailyLoss, 
                                double maxDrawdown, int maxPositions, double maxLotSize);
    
    // Risk checking methods
    bool              CheckRiskLimits();
    bool              CheckPositionRisk(double lotSize, ENUM_ORDER_TYPE orderType);
    bool              CheckDailyLoss();
    bool              CheckDrawdown();
    bool              CheckMaxPositions();
    bool              CheckMaxExposure(double additionalLots);
    
    // Position sizing
    double            CalculatePositionSize(double stopLoss, double riskAmount = 0);
    double            GetOptimalLotSize(double entryPrice, double stopLoss);
    double            GetMaxAllowedLotSize();
    
    // Risk monitoring
    void              UpdateRiskMetrics();
    void              UpdateDailyPnL();
    void              ResetDailyCounters();
    
    // Getters
    double            GetCurrentDrawdown() { return m_currentDrawdown; }
    double            GetDailyPnL() { return m_dailyPnL; }
    int               GetCurrentPositions() { return m_currentPositions; }
    double            GetTotalExposure() { return m_totalExposure; }
    bool              IsRiskLimitExceeded() { return m_riskLimitExceeded; }
    
    // Emergency functions
    void              EmergencyCloseAll();
    void              ReduceExposure(double targetReduction);
    
    // Reporting
    string            GetRiskReport();
    void              LogRiskStatus();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CHFTRiskManager::CHFTRiskManager()
{
    m_riskPercent = 1.0;
    m_maxDailyLoss = 5.0;
    m_maxDrawdown = 10.0;
    m_maxPositions = 5;
    m_maxLotSize = 1.0;
    
    m_startBalance = 0;
    m_dayStartBalance = 0;
    m_peakBalance = 0;
    m_currentDrawdown = 0;
    m_dailyPnL = 0;
    
    m_currentPositions = 0;
    m_totalExposure = 0;
    
    m_riskLimitExceeded = false;
    m_dailyLimitExceeded = false;
    m_drawdownLimitExceeded = false;
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CHFTRiskManager::~CHFTRiskManager()
{
}

//+------------------------------------------------------------------+
//| Initialize risk manager                                          |
//+------------------------------------------------------------------+
bool CHFTRiskManager::Initialize(double riskPercent, double maxDailyLoss, 
                                double maxDrawdown, int maxPositions, double maxLotSize)
{
    m_riskPercent = riskPercent;
    m_maxDailyLoss = maxDailyLoss;
    m_maxDrawdown = maxDrawdown;
    m_maxPositions = maxPositions;
    m_maxLotSize = maxLotSize;
    
    m_startBalance = account.Balance();
    m_dayStartBalance = account.Balance();
    m_peakBalance = account.Balance();
    
    UpdateRiskMetrics();
    
    Print("Risk Manager initialized:");
    Print("- Risk per trade: ", m_riskPercent, "%");
    Print("- Max daily loss: ", m_maxDailyLoss, "%");
    Print("- Max drawdown: ", m_maxDrawdown, "%");
    Print("- Max positions: ", m_maxPositions);
    Print("- Max lot size: ", m_maxLotSize);
    
    return true;
}

//+------------------------------------------------------------------+
//| Check all risk limits                                           |
//+------------------------------------------------------------------+
bool CHFTRiskManager::CheckRiskLimits()
{
    UpdateRiskMetrics();
    
    // Reset flags
    m_riskLimitExceeded = false;
    m_dailyLimitExceeded = false;
    m_drawdownLimitExceeded = false;
    
    // Check daily loss limit
    if(!CheckDailyLoss())
    {
        m_dailyLimitExceeded = true;
        m_riskLimitExceeded = true;
    }
    
    // Check drawdown limit
    if(!CheckDrawdown())
    {
        m_drawdownLimitExceeded = true;
        m_riskLimitExceeded = true;
    }
    
    // Check maximum positions
    if(!CheckMaxPositions())
    {
        m_riskLimitExceeded = true;
    }
    
    return !m_riskLimitExceeded;
}

//+------------------------------------------------------------------+
//| Check position-specific risk                                    |
//+------------------------------------------------------------------+
bool CHFTRiskManager::CheckPositionRisk(double lotSize, ENUM_ORDER_TYPE orderType)
{
    // Check lot size limit
    if(lotSize > m_maxLotSize)
        return false;
    
    // Check if adding this position would exceed exposure limits
    if(!CheckMaxExposure(lotSize))
        return false;
    
    // Check if we can add another position
    if(m_currentPositions >= m_maxPositions)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check daily loss limit                                          |
//+------------------------------------------------------------------+
bool CHFTRiskManager::CheckDailyLoss()
{
    double maxLossAmount = m_dayStartBalance * m_maxDailyLoss / 100.0;
    return (m_dailyPnL > -maxLossAmount);
}

//+------------------------------------------------------------------+
//| Check drawdown limit                                            |
//+------------------------------------------------------------------+
bool CHFTRiskManager::CheckDrawdown()
{
    double maxDrawdownAmount = m_peakBalance * m_maxDrawdown / 100.0;
    double currentDrawdownAmount = m_peakBalance - account.Balance();
    
    m_currentDrawdown = (currentDrawdownAmount / m_peakBalance) * 100.0;
    
    return (m_currentDrawdown < m_maxDrawdown);
}

//+------------------------------------------------------------------+
//| Check maximum positions                                          |
//+------------------------------------------------------------------+
bool CHFTRiskManager::CheckMaxPositions()
{
    return (m_currentPositions < m_maxPositions);
}

//+------------------------------------------------------------------+
//| Check maximum exposure                                           |
//+------------------------------------------------------------------+
bool CHFTRiskManager::CheckMaxExposure(double additionalLots)
{
    double maxExposure = account.Balance() * 0.1; // 10% of balance
    double newExposure = m_totalExposure + (additionalLots * 100000); // Assuming standard lot
    
    return (newExposure <= maxExposure);
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk                           |
//+------------------------------------------------------------------+
double CHFTRiskManager::CalculatePositionSize(double stopLoss, double riskAmount = 0)
{
    if(riskAmount == 0)
        riskAmount = account.Balance() * m_riskPercent / 100.0;
    
    if(stopLoss <= 0)
        return 0;
    
    double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    
    if(tickValue == 0 || tickSize == 0)
        return 0;
    
    double stopLossInTicks = stopLoss / tickSize;
    double lotSize = riskAmount / (stopLossInTicks * tickValue);
    
    // Apply limits
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxLot = MathMin(m_maxLotSize, SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX));
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
    
    lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
    lotSize = MathFloor(lotSize / lotStep) * lotStep;
    
    return lotSize;
}

//+------------------------------------------------------------------+
//| Get optimal lot size                                            |
//+------------------------------------------------------------------+
double CHFTRiskManager::GetOptimalLotSize(double entryPrice, double stopLoss)
{
    double stopLossDistance = MathAbs(entryPrice - stopLoss);
    return CalculatePositionSize(stopLossDistance);
}

//+------------------------------------------------------------------+
//| Get maximum allowed lot size                                    |
//+------------------------------------------------------------------+
double CHFTRiskManager::GetMaxAllowedLotSize()
{
    double maxByBalance = account.Balance() * 0.02; // 2% of balance
    double maxByRisk = m_maxLotSize;
    
    return MathMin(maxByBalance, maxByRisk);
}

//+------------------------------------------------------------------+
//| Update risk metrics                                             |
//+------------------------------------------------------------------+
void CHFTRiskManager::UpdateRiskMetrics()
{
    // Update peak balance
    if(account.Balance() > m_peakBalance)
        m_peakBalance = account.Balance();
    
    // Update daily P&L
    UpdateDailyPnL();
    
    // Count current positions
    m_currentPositions = 0;
    m_totalExposure = 0;
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol())
            {
                m_currentPositions++;
                m_totalExposure += position.Volume() * 100000; // Standard lot value
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Update daily P&L                                               |
//+------------------------------------------------------------------+
void CHFTRiskManager::UpdateDailyPnL()
{
    m_dailyPnL = account.Balance() - m_dayStartBalance;
}

//+------------------------------------------------------------------+
//| Reset daily counters                                            |
//+------------------------------------------------------------------+
void CHFTRiskManager::ResetDailyCounters()
{
    m_dayStartBalance = account.Balance();
    m_dailyPnL = 0;
    m_dailyLimitExceeded = false;
}

//+------------------------------------------------------------------+
//| Emergency close all positions                                   |
//+------------------------------------------------------------------+
void CHFTRiskManager::EmergencyCloseAll()
{
    Print("EMERGENCY: Closing all positions due to risk limit breach!");
    
    CTrade trade;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(position.SelectByIndex(i))
        {
            if(position.Symbol() == Symbol())
            {
                trade.PositionClose(position.Ticket());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Get risk report                                                 |
//+------------------------------------------------------------------+
string CHFTRiskManager::GetRiskReport()
{
    string report = "=== RISK REPORT ===\n";
    report += StringFormat("Daily P&L: %.2f (%.2f%%)\n", m_dailyPnL, (m_dailyPnL/m_dayStartBalance)*100);
    report += StringFormat("Current Drawdown: %.2f%%\n", m_currentDrawdown);
    report += StringFormat("Active Positions: %d/%d\n", m_currentPositions, m_maxPositions);
    report += StringFormat("Total Exposure: %.2f\n", m_totalExposure);
    report += StringFormat("Risk Limits OK: %s\n", m_riskLimitExceeded ? "NO" : "YES");
    
    return report;
}

//+------------------------------------------------------------------+
//| Log risk status                                                 |
//+------------------------------------------------------------------+
void CHFTRiskManager::LogRiskStatus()
{
    Print(GetRiskReport());
}
