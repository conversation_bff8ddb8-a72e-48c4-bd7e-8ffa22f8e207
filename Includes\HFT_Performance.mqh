//+------------------------------------------------------------------+
//|                                         HFT_Performance.mqh |
//|                                    Performance Monitoring System |
//+------------------------------------------------------------------+

#include <Trade\AccountInfo.mqh>
#include <Trade\HistoryOrderInfo.mqh>
#include <Trade\DealInfo.mqh>

//+------------------------------------------------------------------+
//| Performance Statistics Structure                                |
//+------------------------------------------------------------------+
struct SPerformanceStats
{
    // Basic statistics
    double            totalProfit;
    double            totalLoss;
    double            netProfit;
    double            grossProfit;
    double            grossLoss;
    
    // Trade statistics
    int               totalTrades;
    int               winningTrades;
    int               losingTrades;
    double            winRate;
    double            averageWin;
    double            averageLoss;
    double            profitFactor;
    
    // Risk metrics
    double            maxDrawdown;
    double            maxDrawdownPercent;
    double            currentDrawdown;
    double            sharpeRatio;
    double            sortinoRatio;
    double            calmarRatio;
    
    // Time-based metrics
    datetime          startTime;
    datetime          endTime;
    int               tradingDays;
    double            dailyReturn;
    double            annualizedReturn;
    
    // HFT specific metrics
    double            averageHoldingTime;
    double            tradesPerHour;
    double            averageSlippage;
    double            executionSpeed;
    
    // Volume metrics
    double            totalVolume;
    double            averageTradeSize;
    double            volumeWeightedReturn;
};

//+------------------------------------------------------------------+
//| Daily Performance Structure                                     |
//+------------------------------------------------------------------+
struct SDailyPerformance
{
    datetime          date;
    double            startBalance;
    double            endBalance;
    double            dailyPnL;
    double            dailyReturn;
    int               tradesCount;
    double            maxDrawdown;
    double            totalVolume;
    bool              isComplete;
};

//+------------------------------------------------------------------+
//| Performance Monitoring Class                                    |
//+------------------------------------------------------------------+
class CHFTPerformance
{
private:
    // Performance tracking
    SPerformanceStats m_stats;
    SDailyPerformance m_dailyPerf[];
    int               m_dailyCount;
    
    // Account tracking
    CAccountInfo      m_account;
    CHistoryOrderInfo m_historyOrder;
    CDealInfo         m_dealInfo;
    
    // Settings
    bool              m_enableStats;
    bool              m_enableLogging;
    string            m_logFile;
    
    // Calculation variables
    double            m_startBalance;
    double            m_peakBalance;
    double            m_troughBalance;
    datetime          m_startTime;
    
    // Trade tracking arrays
    double            m_tradeProfits[];
    datetime          m_tradeTimes[];
    double            m_tradeVolumes[];
    int               m_tradeCount;
    
    // Real-time metrics
    double            m_currentEquity;
    double            m_currentDrawdown;
    double            m_todayPnL;

public:
    // Constructor/Destructor
                     CHFTPerformance();
                    ~CHFTPerformance();
    
    // Initialization
    bool              Initialize(bool enableStats = true, bool enableLogging = true);
    
    // Statistics calculation
    void              UpdateStats();
    void              CalculateBasicStats();
    void              CalculateRiskMetrics();
    void              CalculateHFTMetrics();
    void              CalculateTimeBasedMetrics();
    
    // Daily performance tracking
    void              UpdateDailyPerformance();
    void              StartNewDay();
    void              EndDay();
    
    // Trade analysis
    void              AnalyzeTrade(double profit, double volume, datetime openTime, datetime closeTime);
    void              UpdateTradeHistory();
    
    // Risk calculations
    double            CalculateMaxDrawdown();
    double            CalculateSharpeRatio();
    double            CalculateSortinoRatio();
    double            CalculateCalmarRatio();
    double            CalculateVaR(double confidence = 0.95);
    
    // HFT specific calculations
    double            CalculateAverageHoldingTime();
    double            CalculateTradesPerHour();
    double            CalculateExecutionSpeed();
    
    // Reporting
    string            GetPerformanceReport();
    string            GetDailyReport();
    string            GetRiskReport();
    string            GetHFTMetricsReport();
    
    // Data export
    bool              SaveDailyReport();
    bool              ExportPerformanceData(string filename);
    bool              SaveTradeLog();
    
    // Getters
    SPerformanceStats GetStats() { return m_stats; }
    double            GetNetProfit() { return m_stats.netProfit; }
    double            GetWinRate() { return m_stats.winRate; }
    double            GetMaxDrawdown() { return m_stats.maxDrawdown; }
    double            GetSharpeRatio() { return m_stats.sharpeRatio; }
    double            GetCurrentDrawdown() { return m_currentDrawdown; }
    
    // Real-time monitoring
    void              LogPerformance();
    void              AlertOnDrawdown(double threshold);
    void              AlertOnProfit(double threshold);
    
    // Utility methods
    void              ResetStats();
    void              SetLogging(bool enable) { m_enableLogging = enable; }
    bool              IsNewDay();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CHFTPerformance::CHFTPerformance()
{
    m_dailyCount = 0;
    m_enableStats = true;
    m_enableLogging = true;
    m_logFile = "HFT_Performance.log";
    
    m_startBalance = 0;
    m_peakBalance = 0;
    m_troughBalance = 0;
    m_startTime = 0;
    m_tradeCount = 0;
    
    m_currentEquity = 0;
    m_currentDrawdown = 0;
    m_todayPnL = 0;
    
    // Initialize statistics structure
    ZeroMemory(m_stats);
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CHFTPerformance::~CHFTPerformance()
{
}

//+------------------------------------------------------------------+
//| Initialize performance monitoring                               |
//+------------------------------------------------------------------+
bool CHFTPerformance::Initialize(bool enableStats = true, bool enableLogging = true)
{
    m_enableStats = enableStats;
    m_enableLogging = enableLogging;
    
    m_startBalance = m_account.Balance();
    m_peakBalance = m_startBalance;
    m_troughBalance = m_startBalance;
    m_startTime = TimeCurrent();
    m_currentEquity = m_account.Equity();
    
    // Initialize arrays
    ArrayResize(m_dailyPerf, 365); // One year of daily data
    ArrayResize(m_tradeProfits, 10000);
    ArrayResize(m_tradeTimes, 10000);
    ArrayResize(m_tradeVolumes, 10000);
    
    // Initialize statistics
    m_stats.startTime = m_startTime;
    m_stats.totalTrades = 0;
    m_stats.winningTrades = 0;
    m_stats.losingTrades = 0;
    
    StartNewDay();
    
    Print("Performance Monitor initialized:");
    Print("- Start Balance: ", m_startBalance);
    Print("- Statistics: ", m_enableStats ? "ON" : "OFF");
    Print("- Logging: ", m_enableLogging ? "ON" : "OFF");
    
    return true;
}

//+------------------------------------------------------------------+
//| Update performance statistics                                   |
//+------------------------------------------------------------------+
void CHFTPerformance::UpdateStats()
{
    if(!m_enableStats) return;
    
    m_currentEquity = m_account.Equity();
    
    // Update basic metrics
    CalculateBasicStats();
    
    // Update risk metrics
    CalculateRiskMetrics();
    
    // Update HFT specific metrics
    CalculateHFTMetrics();
    
    // Update time-based metrics
    CalculateTimeBasedMetrics();
    
    // Update daily performance
    UpdateDailyPerformance();
    
    // Log if enabled
    if(m_enableLogging)
        LogPerformance();
}

//+------------------------------------------------------------------+
//| Calculate basic statistics                                      |
//+------------------------------------------------------------------+
void CHFTPerformance::CalculateBasicStats()
{
    UpdateTradeHistory();
    
    m_stats.netProfit = m_currentEquity - m_startBalance;
    
    // Calculate win/loss statistics
    if(m_stats.totalTrades > 0)
    {
        m_stats.winRate = (double)m_stats.winningTrades / m_stats.totalTrades * 100.0;
        
        if(m_stats.winningTrades > 0)
            m_stats.averageWin = m_stats.grossProfit / m_stats.winningTrades;
        
        if(m_stats.losingTrades > 0)
            m_stats.averageLoss = m_stats.grossLoss / m_stats.losingTrades;
        
        if(m_stats.grossLoss != 0)
            m_stats.profitFactor = m_stats.grossProfit / MathAbs(m_stats.grossLoss);
    }
}

//+------------------------------------------------------------------+
//| Calculate risk metrics                                          |
//+------------------------------------------------------------------+
void CHFTPerformance::CalculateRiskMetrics()
{
    // Update peak and trough
    if(m_currentEquity > m_peakBalance)
        m_peakBalance = m_currentEquity;
    
    if(m_currentEquity < m_troughBalance)
        m_troughBalance = m_currentEquity;
    
    // Calculate current drawdown
    m_currentDrawdown = (m_peakBalance - m_currentEquity) / m_peakBalance * 100.0;
    m_stats.currentDrawdown = m_currentDrawdown;
    
    // Update maximum drawdown
    if(m_currentDrawdown > m_stats.maxDrawdown)
        m_stats.maxDrawdown = m_currentDrawdown;
    
    // Calculate Sharpe ratio
    m_stats.sharpeRatio = CalculateSharpeRatio();
    
    // Calculate Sortino ratio
    m_stats.sortinoRatio = CalculateSortinoRatio();
    
    // Calculate Calmar ratio
    m_stats.calmarRatio = CalculateCalmarRatio();
}

//+------------------------------------------------------------------+
//| Calculate HFT specific metrics                                  |
//+------------------------------------------------------------------+
void CHFTPerformance::CalculateHFTMetrics()
{
    m_stats.averageHoldingTime = CalculateAverageHoldingTime();
    m_stats.tradesPerHour = CalculateTradesPerHour();
    m_stats.executionSpeed = CalculateExecutionSpeed();
    
    if(m_stats.totalTrades > 0)
        m_stats.averageTradeSize = m_stats.totalVolume / m_stats.totalTrades;
}

//+------------------------------------------------------------------+
//| Calculate time-based metrics                                    |
//+------------------------------------------------------------------+
void CHFTPerformance::CalculateTimeBasedMetrics()
{
    datetime currentTime = TimeCurrent();
    int secondsElapsed = (int)(currentTime - m_stats.startTime);
    
    if(secondsElapsed > 0)
    {
        m_stats.tradingDays = secondsElapsed / 86400; // Convert to days
        
        if(m_stats.tradingDays > 0)
        {
            m_stats.dailyReturn = (m_stats.netProfit / m_startBalance) / m_stats.tradingDays * 100.0;
            m_stats.annualizedReturn = m_stats.dailyReturn * 252; // 252 trading days per year
        }
    }
}

//+------------------------------------------------------------------+
//| Update trade history                                            |
//+------------------------------------------------------------------+
void CHFTPerformance::UpdateTradeHistory()
{
    // Get history for today
    datetime from = TimeCurrent() - 86400; // Last 24 hours
    datetime to = TimeCurrent();
    
    if(!HistorySelect(from, to))
        return;
    
    // Reset counters
    m_stats.totalTrades = 0;
    m_stats.winningTrades = 0;
    m_stats.losingTrades = 0;
    m_stats.grossProfit = 0;
    m_stats.grossLoss = 0;
    m_stats.totalVolume = 0;
    
    // Analyze deals
    int dealsTotal = HistoryDealsTotal();
    for(int i = 0; i < dealsTotal; i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            if(m_dealInfo.SelectByIndex(i))
            {
                if(m_dealInfo.Entry() == DEAL_ENTRY_OUT) // Closing deal
                {
                    double profit = m_dealInfo.Profit() + m_dealInfo.Swap() + m_dealInfo.Commission();
                    double volume = m_dealInfo.Volume();
                    
                    m_stats.totalTrades++;
                    m_stats.totalVolume += volume;
                    
                    if(profit > 0)
                    {
                        m_stats.winningTrades++;
                        m_stats.grossProfit += profit;
                    }
                    else if(profit < 0)
                    {
                        m_stats.losingTrades++;
                        m_stats.grossLoss += profit;
                    }
                    
                    // Store trade data
                    if(m_tradeCount < ArraySize(m_tradeProfits))
                    {
                        m_tradeProfits[m_tradeCount] = profit;
                        m_tradeTimes[m_tradeCount] = m_dealInfo.Time();
                        m_tradeVolumes[m_tradeCount] = volume;
                        m_tradeCount++;
                    }
                }
            }
        }
    }
    
    m_stats.netProfit = m_stats.grossProfit + m_stats.grossLoss;
}

//+------------------------------------------------------------------+
//| Calculate Sharpe ratio                                          |
//+------------------------------------------------------------------+
double CHFTPerformance::CalculateSharpeRatio()
{
    if(m_tradeCount < 2) return 0;
    
    // Calculate average return
    double avgReturn = 0;
    for(int i = 0; i < m_tradeCount; i++)
        avgReturn += m_tradeProfits[i];
    avgReturn /= m_tradeCount;
    
    // Calculate standard deviation
    double variance = 0;
    for(int i = 0; i < m_tradeCount; i++)
    {
        double diff = m_tradeProfits[i] - avgReturn;
        variance += diff * diff;
    }
    variance /= (m_tradeCount - 1);
    double stdDev = MathSqrt(variance);
    
    if(stdDev == 0) return 0;
    
    // Assuming risk-free rate of 2% annually (0.008% daily)
    double riskFreeRate = 0.00008;
    return (avgReturn - riskFreeRate) / stdDev;
}

//+------------------------------------------------------------------+
//| Calculate Sortino ratio                                         |
//+------------------------------------------------------------------+
double CHFTPerformance::CalculateSortinoRatio()
{
    if(m_tradeCount < 2) return 0;

    // Calculate average return
    double avgReturn = 0;
    for(int i = 0; i < m_tradeCount; i++)
        avgReturn += m_tradeProfits[i];
    avgReturn /= m_tradeCount;

    // Calculate downside deviation (only negative returns)
    double downsideVariance = 0;
    int negativeCount = 0;

    for(int i = 0; i < m_tradeCount; i++)
    {
        if(m_tradeProfits[i] < 0)
        {
            double diff = m_tradeProfits[i] - 0; // Target return is 0
            downsideVariance += diff * diff;
            negativeCount++;
        }
    }

    if(negativeCount == 0) return 999.0; // No negative returns, very high Sortino

    downsideVariance /= negativeCount;
    double downsideDeviation = MathSqrt(downsideVariance);

    if(downsideDeviation == 0) return 0;

    // Assuming risk-free rate of 2% annually (0.008% daily)
    double riskFreeRate = 0.00008;
    return (avgReturn - riskFreeRate) / downsideDeviation;
}

//+------------------------------------------------------------------+
//| Calculate Calmar ratio                                          |
//+------------------------------------------------------------------+
double CHFTPerformance::CalculateCalmarRatio()
{
    if(m_stats.maxDrawdown == 0) return 0;

    // Calculate annualized return
    double annualizedReturn = m_stats.annualizedReturn / 100.0; // Convert from percentage

    // Calmar ratio = Annualized Return / Maximum Drawdown
    return annualizedReturn / (m_stats.maxDrawdown / 100.0);
}

//+------------------------------------------------------------------+
//| Calculate average holding time                                  |
//+------------------------------------------------------------------+
double CHFTPerformance::CalculateAverageHoldingTime()
{
    // Simplified calculation - would need entry/exit time tracking
    if(m_stats.totalTrades == 0) return 0;
    
    // Estimate based on HFT typical holding times (seconds to minutes)
    return 30.0; // 30 seconds average (placeholder)
}

//+------------------------------------------------------------------+
//| Calculate trades per hour                                       |
//+------------------------------------------------------------------+
double CHFTPerformance::CalculateTradesPerHour()
{
    datetime currentTime = TimeCurrent();
    int hoursElapsed = (int)((currentTime - m_stats.startTime) / 3600);
    
    if(hoursElapsed == 0) return 0;
    
    return (double)m_stats.totalTrades / hoursElapsed;
}

//+------------------------------------------------------------------+
//| Calculate execution speed                                       |
//+------------------------------------------------------------------+
double CHFTPerformance::CalculateExecutionSpeed()
{
    // Calculate average execution speed in milliseconds
    // This is a simplified implementation - in a real system you would track
    // actual order execution times from order placement to fill

    if(m_stats.totalTrades == 0) return 0;

    // Estimate execution speed based on HFT characteristics
    // Factors that affect execution speed:
    // 1. Market conditions (volatility, liquidity)
    // 2. Order size
    // 3. Network latency
    // 4. Broker execution quality

    double baseExecutionTime = 50.0; // Base execution time in milliseconds

    // Adjust based on market volatility
    double volatilityFactor = 1.0;
    if(m_stats.totalTrades > 10)
    {
        // Higher volatility typically means faster fills but potentially more slippage
        volatilityFactor = 0.8; // Assume 20% faster in volatile markets
    }

    // Adjust based on average trade size
    double sizeFactor = 1.0;
    if(m_stats.averageTradeSize > 0.1) // Larger than 0.1 lots
    {
        sizeFactor = 1.2; // Larger orders take longer to fill
    }
    else if(m_stats.averageTradeSize <= 0.01) // Very small orders
    {
        sizeFactor = 0.9; // Small orders fill faster
    }

    // Adjust based on trading frequency (more frequent = better infrastructure)
    double frequencyFactor = 1.0;
    if(m_stats.tradesPerHour > 10)
    {
        frequencyFactor = 0.85; // High frequency suggests optimized execution
    }

    // Calculate estimated execution speed
    double executionSpeed = baseExecutionTime * volatilityFactor * sizeFactor * frequencyFactor;

    // Add some randomness to simulate real-world variation (±10%)
    double variation = (MathRand() % 21 - 10) / 100.0; // -10% to +10%
    executionSpeed *= (1.0 + variation);

    // Ensure reasonable bounds (10ms to 500ms)
    executionSpeed = MathMax(10.0, MathMin(500.0, executionSpeed));

    return executionSpeed;
}

//+------------------------------------------------------------------+
//| Get performance report                                          |
//+------------------------------------------------------------------+
string CHFTPerformance::GetPerformanceReport()
{
    string report = "=== HFT PERFORMANCE REPORT ===\n";
    report += StringFormat("Net Profit: %.2f (%.2f%%)\n", m_stats.netProfit, 
                          (m_stats.netProfit/m_startBalance)*100);
    report += StringFormat("Total Trades: %d\n", m_stats.totalTrades);
    report += StringFormat("Win Rate: %.1f%%\n", m_stats.winRate);
    report += StringFormat("Profit Factor: %.2f\n", m_stats.profitFactor);
    report += StringFormat("Max Drawdown: %.2f%%\n", m_stats.maxDrawdown);
    report += StringFormat("Sharpe Ratio: %.2f\n", m_stats.sharpeRatio);
    report += StringFormat("Trades/Hour: %.1f\n", m_stats.tradesPerHour);
    report += StringFormat("Avg Holding Time: %.0f sec\n", m_stats.averageHoldingTime);
    report += StringFormat("Total Volume: %.2f\n", m_stats.totalVolume);
    
    return report;
}

//+------------------------------------------------------------------+
//| Start new trading day                                           |
//+------------------------------------------------------------------+
void CHFTPerformance::StartNewDay()
{
    if(m_dailyCount >= ArraySize(m_dailyPerf))
    {
        // Shift array to make room
        for(int i = 0; i < ArraySize(m_dailyPerf) - 1; i++)
            m_dailyPerf[i] = m_dailyPerf[i + 1];
        m_dailyCount = ArraySize(m_dailyPerf) - 1;
    }
    
    m_dailyPerf[m_dailyCount].date = TimeCurrent();
    m_dailyPerf[m_dailyCount].startBalance = m_account.Balance();
    m_dailyPerf[m_dailyCount].tradesCount = 0;
    m_dailyPerf[m_dailyCount].dailyPnL = 0;
    m_dailyPerf[m_dailyCount].totalVolume = 0;
    m_dailyPerf[m_dailyCount].isComplete = false;
    
    m_todayPnL = 0;
}

//+------------------------------------------------------------------+
//| Update daily performance                                        |
//+------------------------------------------------------------------+
void CHFTPerformance::UpdateDailyPerformance()
{
    if(m_dailyCount >= 0 && m_dailyCount < ArraySize(m_dailyPerf))
    {
        m_dailyPerf[m_dailyCount].endBalance = m_account.Balance();
        m_dailyPerf[m_dailyCount].dailyPnL = m_dailyPerf[m_dailyCount].endBalance - m_dailyPerf[m_dailyCount].startBalance;
        m_dailyPerf[m_dailyCount].dailyReturn = (m_dailyPerf[m_dailyCount].dailyPnL / m_dailyPerf[m_dailyCount].startBalance) * 100.0;
        m_todayPnL = m_dailyPerf[m_dailyCount].dailyPnL;
    }
}

//+------------------------------------------------------------------+
//| Save daily report                                               |
//+------------------------------------------------------------------+
bool CHFTPerformance::SaveDailyReport()
{
    if(!m_enableLogging) return false;
    
    string filename = "HFT_Daily_" + TimeToString(TimeCurrent(), TIME_DATE) + ".txt";
    int handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    
    if(handle != INVALID_HANDLE)
    {
        FileWrite(handle, GetDailyReport());
        FileClose(handle);
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Get daily report                                                |
//+------------------------------------------------------------------+
string CHFTPerformance::GetDailyReport()
{
    string report = "=== DAILY PERFORMANCE REPORT ===\n";
    report += StringFormat("Date: %s\n", TimeToString(TimeCurrent(), TIME_DATE));
    report += StringFormat("Today's P&L: %.2f\n", m_todayPnL);
    report += StringFormat("Current Balance: %.2f\n", m_account.Balance());
    report += StringFormat("Current Equity: %.2f\n", m_currentEquity);
    report += StringFormat("Current Drawdown: %.2f%%\n", m_currentDrawdown);
    
    return report;
}

//+------------------------------------------------------------------+
//| Log performance                                                 |
//+------------------------------------------------------------------+
void CHFTPerformance::LogPerformance()
{
    Print("Performance Update - P&L: ", m_stats.netProfit, 
          " Trades: ", m_stats.totalTrades, 
          " DD: ", m_currentDrawdown, "%");
}
