//+------------------------------------------------------------------+
//|                                               HFT_Config.mqh |
//|                                    HFT Configuration Settings |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Global Configuration Constants                                  |
//+------------------------------------------------------------------+
#define HFT_VERSION "1.0.0"
#define HFT_BUILD_DATE "2025-07-11"

//+------------------------------------------------------------------+
//| Trading Configuration                                           |
//+------------------------------------------------------------------+
struct SHFTConfig
{
    // General Settings
    bool              enableTrading;
    int               magicNumber;
    string            tradeComment;
    double            maxSpread;
    
    // Risk Management
    double            riskPercent;
    double            maxDailyLoss;
    double            maxDrawdown;
    int               maxPositions;
    double            maxLotSize;
    double            emergencyStopLevel;
    
    // Strategy Settings
    bool              enableArbitrage;
    bool              enableMarketMaking;
    bool              enableMomentum;
    bool              enableScalping;
    
    // Market Making
    double            spreadMultiplier;
    int               quoteDepth;
    double            minProfitPoints;
    double            marketMakingVolume;
    int               maxQuoteUpdatesPerSecond;
    
    // Momentum Strategy
    int               momentumPeriod;
    double            momentumThreshold;
    int               fastMA;
    int               slowMA;
    double            momentumVolume;
    
    // Scalping Strategy
    double            scalpingTarget;
    double            scalpingStop;
    int               scalpingTimeframe;
    double            scalpingVolume;
    double            scalpingRSILevel;
    
    // Arbitrage Strategy
    double            minArbitrageSpread;
    double            maxArbitrageRisk;
    double            arbitrageVolume;
    int               arbitrageCheckInterval;
    
    // Performance & Monitoring
    bool              enableLogging;
    bool              enableStats;
    int               statsUpdateInterval;
    bool              enableAlerts;
    double            drawdownAlertLevel;
    double            profitAlertLevel;
    
    // Execution Settings
    ulong             maxSlippage;
    bool              useMarketExecution;
    int               maxRetriesPerOrder;
    int               orderTimeoutSeconds;
    
    // Market Data Settings
    bool              fastMode;
    int               tickStorageSize;
    int               dataUpdateInterval;
    bool              enableMicrostructureAnalysis;
    
    // Time Filters
    bool              enableTimeFilter;
    int               tradingStartHour;
    int               tradingEndHour;
    bool              avoidNews;
    bool              avoidWeekends;
    
    // Symbol Specific
    string            tradingSymbols[];
    double            symbolMultipliers[];
    bool              enableMultiSymbol;
};

//+------------------------------------------------------------------+
//| Default Configuration Values                                    |
//+------------------------------------------------------------------+
class CHFTConfig
{
private:
    SHFTConfig        m_config;
    string            m_configFile;
    bool              m_isLoaded;

public:
    // Constructor
                     CHFTConfig();
    
    // Configuration management
    bool              LoadConfig(string filename = "HFT_Config.ini");
    bool              SaveConfig(string filename = "HFT_Config.ini");
    void              SetDefaultConfig();
    bool              ValidateConfig();
    
    // Getters
    SHFTConfig        GetConfig() { return m_config; }
    bool              IsConfigLoaded() { return m_isLoaded; }
    
    // Individual parameter access
    bool              GetEnableTrading() { return m_config.enableTrading; }
    double            GetRiskPercent() { return m_config.riskPercent; }
    double            GetMaxSpread() { return m_config.maxSpread; }
    int               GetMagicNumber() { return m_config.magicNumber; }
    
    // Setters
    void              SetEnableTrading(bool enable) { m_config.enableTrading = enable; }
    void              SetRiskPercent(double risk) { m_config.riskPercent = risk; }
    void              SetMaxSpread(double spread) { m_config.maxSpread = spread; }
    
    // Configuration validation
    bool              ValidateRiskSettings();
    bool              ValidateStrategySettings();
    bool              ValidateExecutionSettings();
    
    // Utility methods
    string            GetConfigSummary();
    void              PrintConfig();
    void              ResetToDefaults();
    void              SetConfigValue(string key, string value);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CHFTConfig::CHFTConfig()
{
    m_configFile = "HFT_Config.ini";
    m_isLoaded = false;
    SetDefaultConfig();
}

//+------------------------------------------------------------------+
//| Set default configuration                                       |
//+------------------------------------------------------------------+
void CHFTConfig::SetDefaultConfig()
{
    // General Settings
    m_config.enableTrading = true;
    m_config.magicNumber = 123456;
    m_config.tradeComment = "HFT_Pro";
    m_config.maxSpread = 3.0;
    
    // Risk Management
    m_config.riskPercent = 1.0;
    m_config.maxDailyLoss = 5.0;
    m_config.maxDrawdown = 10.0;
    m_config.maxPositions = 5;
    m_config.maxLotSize = 1.0;
    m_config.emergencyStopLevel = 15.0;
    
    // Strategy Settings
    m_config.enableArbitrage = true;
    m_config.enableMarketMaking = true;
    m_config.enableMomentum = true;
    m_config.enableScalping = true;
    
    // Market Making
    m_config.spreadMultiplier = 1.5;
    m_config.quoteDepth = 5;
    m_config.minProfitPoints = 2.0;
    m_config.marketMakingVolume = 0.01;
    m_config.maxQuoteUpdatesPerSecond = 10;
    
    // Momentum Strategy
    m_config.momentumPeriod = 10;
    m_config.momentumThreshold = 0.0001;
    m_config.fastMA = 5;
    m_config.slowMA = 20;
    m_config.momentumVolume = 0.01;
    
    // Scalping Strategy
    m_config.scalpingTarget = 5.0;
    m_config.scalpingStop = 10.0;
    m_config.scalpingTimeframe = 1;
    m_config.scalpingVolume = 0.01;
    m_config.scalpingRSILevel = 70.0;
    
    // Arbitrage Strategy
    m_config.minArbitrageSpread = 1.0;
    m_config.maxArbitrageRisk = 2.0;
    m_config.arbitrageVolume = 0.01;
    m_config.arbitrageCheckInterval = 1;
    
    // Performance & Monitoring
    m_config.enableLogging = true;
    m_config.enableStats = true;
    m_config.statsUpdateInterval = 60;
    m_config.enableAlerts = true;
    m_config.drawdownAlertLevel = 5.0;
    m_config.profitAlertLevel = 10.0;
    
    // Execution Settings
    m_config.maxSlippage = 3;
    m_config.useMarketExecution = true;
    m_config.maxRetriesPerOrder = 3;
    m_config.orderTimeoutSeconds = 30;
    
    // Market Data Settings
    m_config.fastMode = true;
    m_config.tickStorageSize = 1000;
    m_config.dataUpdateInterval = 1;
    m_config.enableMicrostructureAnalysis = true;
    
    // Time Filters
    m_config.enableTimeFilter = false;
    m_config.tradingStartHour = 8;
    m_config.tradingEndHour = 18;
    m_config.avoidNews = true;
    m_config.avoidWeekends = true;
    
    // Symbol Specific
    m_config.enableMultiSymbol = false;
    ArrayResize(m_config.tradingSymbols, 1);
    ArrayResize(m_config.symbolMultipliers, 1);
    m_config.tradingSymbols[0] = Symbol();
    m_config.symbolMultipliers[0] = 1.0;
}

//+------------------------------------------------------------------+
//| Load configuration from file                                   |
//+------------------------------------------------------------------+
bool CHFTConfig::LoadConfig(string filename = "HFT_Config.ini")
{
    m_configFile = filename;
    
    // Check if file exists
    if(!FileIsExist(filename))
    {
        Print("Config file not found, creating default: ", filename);
        SaveConfig(filename);
        m_isLoaded = true;
        return true;
    }
    
    // Read configuration from file
    int handle = FileOpen(filename, FILE_READ | FILE_TXT);
    if(handle == INVALID_HANDLE)
    {
        Print("Error opening config file: ", filename);
        return false;
    }
    
    // Parse configuration file
    while(!FileIsEnding(handle))
    {
        string line = FileReadString(handle);
        if(StringLen(line) == 0 || StringGetCharacter(line, 0) == '#')
            continue;
        
        // Parse key=value pairs
        int pos = StringFind(line, "=");
        if(pos > 0)
        {
            string key = StringSubstr(line, 0, pos);
            string value = StringSubstr(line, pos + 1);
            
            // Remove whitespace
            StringTrimLeft(key);
            StringTrimRight(key);
            StringTrimLeft(value);
            StringTrimRight(value);
            
            // Set configuration values
            SetConfigValue(key, value);
        }
    }
    
    FileClose(handle);
    
    if(ValidateConfig())
    {
        m_isLoaded = true;
        Print("Configuration loaded successfully from: ", filename);
        return true;
    }
    else
    {
        Print("Configuration validation failed, using defaults");
        SetDefaultConfig();
        return false;
    }
}

//+------------------------------------------------------------------+
//| Save configuration to file                                     |
//+------------------------------------------------------------------+
bool CHFTConfig::SaveConfig(string filename = "HFT_Config.ini")
{
    int handle = FileOpen(filename, FILE_WRITE | FILE_TXT);
    if(handle == INVALID_HANDLE)
    {
        Print("Error creating config file: ", filename);
        return false;
    }
    
    // Write header
    FileWrite(handle, "# HFT Professional EA Configuration File");
    FileWrite(handle, "# Generated on: " + TimeToString(TimeCurrent()));
    FileWrite(handle, "# Version: " + HFT_VERSION);
    FileWrite(handle, "");
    
    // Write configuration sections
    FileWrite(handle, "[General]");
    FileWrite(handle, "EnableTrading=" + (string)m_config.enableTrading);
    FileWrite(handle, "MagicNumber=" + (string)m_config.magicNumber);
    FileWrite(handle, "TradeComment=" + m_config.tradeComment);
    FileWrite(handle, "MaxSpread=" + DoubleToString(m_config.maxSpread, 1));
    FileWrite(handle, "");
    
    FileWrite(handle, "[Risk Management]");
    FileWrite(handle, "RiskPercent=" + DoubleToString(m_config.riskPercent, 1));
    FileWrite(handle, "MaxDailyLoss=" + DoubleToString(m_config.maxDailyLoss, 1));
    FileWrite(handle, "MaxDrawdown=" + DoubleToString(m_config.maxDrawdown, 1));
    FileWrite(handle, "MaxPositions=" + (string)m_config.maxPositions);
    FileWrite(handle, "MaxLotSize=" + DoubleToString(m_config.maxLotSize, 2));
    FileWrite(handle, "");
    
    FileWrite(handle, "[Strategies]");
    FileWrite(handle, "EnableArbitrage=" + (string)m_config.enableArbitrage);
    FileWrite(handle, "EnableMarketMaking=" + (string)m_config.enableMarketMaking);
    FileWrite(handle, "EnableMomentum=" + (string)m_config.enableMomentum);
    FileWrite(handle, "EnableScalping=" + (string)m_config.enableScalping);
    FileWrite(handle, "");
    
    FileWrite(handle, "[Performance]");
    FileWrite(handle, "EnableLogging=" + (string)m_config.enableLogging);
    FileWrite(handle, "EnableStats=" + (string)m_config.enableStats);
    FileWrite(handle, "StatsUpdateInterval=" + (string)m_config.statsUpdateInterval);
    FileWrite(handle, "");
    
    FileClose(handle);
    Print("Configuration saved to: ", filename);
    return true;
}

//+------------------------------------------------------------------+
//| Set configuration value from string                            |
//+------------------------------------------------------------------+
void CHFTConfig::SetConfigValue(string key, string value)
{
    // General settings
    if(key == "EnableTrading") m_config.enableTrading = (bool)StringToInteger(value);
    else if(key == "MagicNumber") m_config.magicNumber = (int)StringToInteger(value);
    else if(key == "TradeComment") m_config.tradeComment = value;
    else if(key == "MaxSpread") m_config.maxSpread = StringToDouble(value);
    
    // Risk management
    else if(key == "RiskPercent") m_config.riskPercent = StringToDouble(value);
    else if(key == "MaxDailyLoss") m_config.maxDailyLoss = StringToDouble(value);
    else if(key == "MaxDrawdown") m_config.maxDrawdown = StringToDouble(value);
    else if(key == "MaxPositions") m_config.maxPositions = (int)StringToInteger(value);
    else if(key == "MaxLotSize") m_config.maxLotSize = StringToDouble(value);
    
    // Strategies
    else if(key == "EnableArbitrage") m_config.enableArbitrage = (bool)StringToInteger(value);
    else if(key == "EnableMarketMaking") m_config.enableMarketMaking = (bool)StringToInteger(value);
    else if(key == "EnableMomentum") m_config.enableMomentum = (bool)StringToInteger(value);
    else if(key == "EnableScalping") m_config.enableScalping = (bool)StringToInteger(value);
    
    // Performance
    else if(key == "EnableLogging") m_config.enableLogging = (bool)StringToInteger(value);
    else if(key == "EnableStats") m_config.enableStats = (bool)StringToInteger(value);
    else if(key == "StatsUpdateInterval") m_config.statsUpdateInterval = (int)StringToInteger(value);
}

//+------------------------------------------------------------------+
//| Validate configuration                                          |
//+------------------------------------------------------------------+
bool CHFTConfig::ValidateConfig()
{
    bool isValid = true;
    
    // Validate risk settings
    if(!ValidateRiskSettings()) isValid = false;
    
    // Validate strategy settings
    if(!ValidateStrategySettings()) isValid = false;
    
    // Validate execution settings
    if(!ValidateExecutionSettings()) isValid = false;
    
    return isValid;
}

//+------------------------------------------------------------------+
//| Validate risk settings                                         |
//+------------------------------------------------------------------+
bool CHFTConfig::ValidateRiskSettings()
{
    if(m_config.riskPercent <= 0 || m_config.riskPercent > 10)
    {
        Print("Invalid risk percent: ", m_config.riskPercent);
        return false;
    }
    
    if(m_config.maxDailyLoss <= 0 || m_config.maxDailyLoss > 50)
    {
        Print("Invalid max daily loss: ", m_config.maxDailyLoss);
        return false;
    }
    
    if(m_config.maxDrawdown <= 0 || m_config.maxDrawdown > 50)
    {
        Print("Invalid max drawdown: ", m_config.maxDrawdown);
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate strategy settings                                     |
//+------------------------------------------------------------------+
bool CHFTConfig::ValidateStrategySettings()
{
    // At lea ]GetStrategyPerformancest one strategy should be enabled
    if(!m_config.enableArbitrage && !m_config.enableMarketMaking && 
       !m_config.enableMomentum && !m_config.enableScalping)
    {
        Print("No strategies enabled");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate execution settings                                    |
//+------------------------------------------------------------------+
bool CHFTConfig::ValidateExecutionSettings()
{
    if(m_config.maxSlippage > 100)
    {
        Print("Max slippage too high: ", m_config.maxSlippage);
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Get configuration summary                                       |
//+------------------------------------------------------------------+
string CHFTConfig::GetConfigSummary()
{
    string summary = "=== HFT CONFIGURATION SUMMARY ===\n";
    summary += StringFormat("Trading Enabled: %s\n", m_config.enableTrading ? "YES" : "NO");
    summary += StringFormat("Risk per Trade: %.1f%%\n", m_config.riskPercent);
    summary += StringFormat("Max Daily Loss: %.1f%%\n", m_config.maxDailyLoss);
    summary += StringFormat("Max Drawdown: %.1f%%\n", m_config.maxDrawdown);
    summary += StringFormat("Strategies: A:%s MM:%s M:%s S:%s\n", 
                           m_config.enableArbitrage ? "ON" : "OFF",
                           m_config.enableMarketMaking ? "ON" : "OFF",
                           m_config.enableMomentum ? "ON" : "OFF",
                           m_config.enableScalping ? "ON" : "OFF");
    
    return summary;
}

//+------------------------------------------------------------------+
//| Print configuration                                             |
//+------------------------------------------------------------------+
void CHFTConfig::PrintConfig()
{
    Print(GetConfigSummary());
}
