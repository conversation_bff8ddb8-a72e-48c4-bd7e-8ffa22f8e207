//+------------------------------------------------------------------+
//|                                           HFT_Professional_EA.mq5 |
//|                                    Professional HFT Trading Bot |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Professional HFT Bot"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Advanced High-Frequency Trading Expert Advisor"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>
#include <Trade\AccountInfo.mqh>
#include <Trade\SymbolInfo.mqh>

//--- Custom includes
#include "HFT_Config.mqh"
#include "Includes\HFT_RiskManager.mqh"
#include "Includes\HFT_MarketData.mqh"
#include "Includes\HFT_TradingEngine.mqh"
#include "Includes\HFT_Strategies.mqh"
#include "Includes\HFT_Performance.mqh"

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== GENERAL SETTINGS ==="
input bool     EnableTrading = true;                    // Enable Trading
input int      MagicNumber = 123456;                   // Magic Number
input string   TradeComment = "HFT_Pro";               // Trade Comment
input double   MaxSpread = 3.0;                        // Maximum Spread (points)

input group "=== RISK MANAGEMENT ==="
input double   RiskPercent = 1.0;                      // Risk per trade (%)
input double   MaxDailyLoss = 5.0;                     // Max daily loss (%)
input double   MaxDrawdown = 10.0;                     // Max drawdown (%)
input int      MaxPositions = 5;                       // Maximum positions
input double   MaxLotSize = 1.0;                       // Maximum lot size

input group "=== HFT STRATEGY SETTINGS ==="
input bool     EnableArbitrage = true;                 // Enable Arbitrage Strategy
input bool     EnableMarketMaking = true;              // Enable Market Making
input bool     EnableMomentum = true;                  // Enable Momentum Strategy
input bool     EnableScalping = true;                  // Enable Scalping Strategy

input group "=== MARKET MAKING ==="
input double   SpreadMultiplier = 1.5;                 // Spread multiplier for quotes
input int      QuoteDepth = 5;                         // Quote depth levels
input double   MinProfitPoints = 2.0;                  // Minimum profit (points)

input group "=== MOMENTUM SETTINGS ==="
input int      MomentumPeriod = 10;                    // Momentum calculation period
input double   MomentumThreshold = 0.0001;             // Momentum threshold
input int      FastMA = 5;                             // Fast MA period
input int      SlowMA = 20;                            // Slow MA period

input group "=== SCALPING SETTINGS ==="
input double   ScalpingTarget = 5.0;                   // Scalping target (points)
input double   ScalpingStop = 10.0;                    // Scalping stop (points)
input int      ScalpingTimeframe = 1;                  // Scalping timeframe (minutes)

input group "=== PERFORMANCE ==="
input bool     EnableLogging = true;                   // Enable detailed logging
input bool     EnableStats = true;                     // Enable statistics
input int      StatsUpdateInterval = 60;               // Stats update interval (seconds)

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;
CAccountInfo   account;
CSymbolInfo    symbol;

// Custom classes
CHFTConfig         *config;
CHFTRiskManager    *riskManager;
CHFTMarketData     *marketData;
CHFTTradingEngine  *tradingEngine;
CHFTStrategies     *strategies;
CHFTPerformance    *performance;

// Timing variables
datetime lastTick = 0;
datetime lastStatsUpdate = 0;
datetime dayStart = 0;

// Performance tracking
double dailyPnL = 0.0;
double maxDrawdownToday = 0.0;
int tradesCount = 0;
int winningTrades = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== HFT Professional EA Initialization ===");
    
    // Initialize trade object
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    
    // Initialize symbol info
    if(!symbol.Name(Symbol()))
    {
        Print("Error: Failed to initialize symbol info");
        return INIT_FAILED;
    }
    
    // Create custom objects
    config = new CHFTConfig();
    riskManager = new CHFTRiskManager();
    marketData = new CHFTMarketData();
    tradingEngine = new CHFTTradingEngine();
    strategies = new CHFTStrategies();
    performance = new CHFTPerformance();

    // Load configuration
    if(!config.LoadConfig())
    {
        Print("Warning: Using default configuration");
    }
    
    // Initialize components
    if(!InitializeComponents())
    {
        Print("Error: Failed to initialize components");
        return INIT_FAILED;
    }
    
    // Set up initial values
    dayStart = TimeCurrent();
    lastStatsUpdate = TimeCurrent();
    
    Print("HFT Professional EA initialized successfully");
    Print("Symbol: ", Symbol());
    Print("Account Balance: ", account.Balance());
    Print("Account Leverage: ", account.Leverage());
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== HFT Professional EA Deinitialization ===");
    
    // Save performance data
    if(performance != NULL)
        performance.SaveDailyReport();
    
    // Clean up objects
    if(config != NULL) delete config;
    if(riskManager != NULL) delete riskManager;
    if(marketData != NULL) delete marketData;
    if(tradingEngine != NULL) delete tradingEngine;
    if(strategies != NULL) delete strategies;
    if(performance != NULL) delete performance;
    
    Print("HFT Professional EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // High-frequency tick processing
    datetime currentTime = TimeCurrent();
    
    // Skip if same tick (for backtesting)
    if(currentTime == lastTick)
        return;
    lastTick = currentTime;
    
    // Update market data
    if(!marketData.UpdateTick())
        return;
    
    // Check trading conditions
    if(!IsReadyToTrade())
        return;
    
    // Risk management checks
    if(!riskManager.CheckRiskLimits())
    {
        if(EnableLogging)
            Print("Risk limits exceeded - trading suspended");
        return;
    }
    
    // Execute trading strategies
    ExecuteTradingStrategies();
    
    // Update performance statistics
    if(currentTime - lastStatsUpdate >= StatsUpdateInterval)
    {
        UpdatePerformanceStats();
        lastStatsUpdate = currentTime;
    }
    
    // Monitor existing positions
    MonitorPositions();
}

//+------------------------------------------------------------------+
//| Initialize all components                                        |
//+------------------------------------------------------------------+
bool InitializeComponents()
{
    // Initialize risk manager
    if(!riskManager.Initialize(RiskPercent, MaxDailyLoss, MaxDrawdown, MaxPositions, MaxLotSize))
    {
        Print("Error: Failed to initialize risk manager");
        return false;
    }
    
    // Initialize market data handler
    if(!marketData.Initialize(Symbol()))
    {
        Print("Error: Failed to initialize market data handler");
        return false;
    }
    
    // Initialize trading engine
    if(!tradingEngine.Initialize(MagicNumber, TradeComment, MaxSpread))
    {
        Print("Error: Failed to initialize trading engine");
        return false;
    }
    
    // Initialize strategies
    SStrategyConfig strategyConfig;
    strategyConfig.enableArbitrage = EnableArbitrage;
    strategyConfig.enableMarketMaking = EnableMarketMaking;
    strategyConfig.enableMomentum = EnableMomentum;
    strategyConfig.enableScalping = EnableScalping;
    strategyConfig.spreadMultiplier = SpreadMultiplier;
    strategyConfig.quoteDepth = QuoteDepth;
    strategyConfig.minProfitPoints = MinProfitPoints;
    strategyConfig.momentumPeriod = MomentumPeriod;
    strategyConfig.momentumThreshold = MomentumThreshold;
    strategyConfig.fastMA = FastMA;
    strategyConfig.slowMA = SlowMA;
    strategyConfig.scalpingTarget = ScalpingTarget;
    strategyConfig.scalpingStop = ScalpingStop;
    strategyConfig.minArbitrageSpread = 1.0;
    strategyConfig.maxArbitrageRisk = 2.0;

    if(!strategies.Initialize(strategyConfig))
    {
        Print("Error: Failed to initialize strategies");
        return false;
    }

    // Connect components
    tradingEngine.SetRiskManager(riskManager);
    strategies.SetTradingEngine(tradingEngine);
    strategies.SetRiskManager(riskManager);
    
    // Initialize performance tracker
    if(!performance.Initialize(EnableStats, EnableLogging))
    {
        Print("Error: Failed to initialize performance tracker");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if ready to trade                                         |
//+------------------------------------------------------------------+
bool IsReadyToTrade()
{
    if(!EnableTrading)
        return false;
    
    // Check market hours
    if(!IsMarketOpen())
        return false;
    
    // Check spread
    double spread = symbol.Spread() * symbol.Point();
    if(spread > MaxSpread * symbol.Point())
        return false;
    
    // Check connection
    if(!TerminalInfoInteger(TERMINAL_CONNECTED))
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if market is open                                         |
//+------------------------------------------------------------------+
bool IsMarketOpen()
{
    datetime currentTime = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(currentTime, dt);
    
    // Skip weekends
    if(dt.day_of_week == 0 || dt.day_of_week == 6)
        return false;
    
    // Add more sophisticated market hours check here
    return true;
}

//+------------------------------------------------------------------+
//| Execute trading strategies                                       |
//+------------------------------------------------------------------+
void ExecuteTradingStrategies()
{
    // Get market analysis
    SMarketAnalysis analysis = marketData.GetAnalysis();
    
    // Execute strategies based on market conditions
    if(EnableArbitrage)
        strategies.ExecuteArbitrage(analysis);
    
    if(EnableMarketMaking)
        strategies.ExecuteMarketMaking(analysis);
    
    if(EnableMomentum)
        strategies.ExecuteMomentum(analysis);
    
    if(EnableScalping)
        strategies.ExecuteScalping(analysis);
}

//+------------------------------------------------------------------+
//| Monitor existing positions                                       |
//+------------------------------------------------------------------+
void MonitorPositions()
{
    tradingEngine.MonitorPositions();
}

//+------------------------------------------------------------------+
//| Update performance statistics                                   |
//+------------------------------------------------------------------+
void UpdatePerformanceStats()
{
    if(performance != NULL)
        performance.UpdateStats();
}
