//+------------------------------------------------------------------+
//|                                           HFT_Strategies.mqh |
//|                                    Advanced HFT Trading Strategies |
//+------------------------------------------------------------------+

#include "HFT_MarketData.mqh"
#include "HFT_TradingEngine.mqh"
#include "HFT_RiskManager.mqh"

//+------------------------------------------------------------------+
//| Strategy Configuration Structure                                |
//+------------------------------------------------------------------+
struct SStrategyConfig
{
    // Strategy enables
    bool              enableArbitrage;
    bool              enableMarketMaking;
    bool              enableMomentum;
    bool              enableScalping;
    
    // Market making parameters
    double            spreadMultiplier;
    int               quoteDepth;
    double            minProfitPoints;
    
    // Momentum parameters
    int               momentumPeriod;
    double            momentumThreshold;
    int               fastMA;
    int               slowMA;
    
    // Scalping parameters
    double            scalpingTarget;
    double            scalpingStop;
    
    // Arbitrage parameters
    double            minArbitrageSpread;
    double            maxArbitrageRisk;
};

//+------------------------------------------------------------------+
//| Strategy Performance Structure                                  |
//+------------------------------------------------------------------+
struct SStrategyPerformance
{
    string            strategyName;
    int               totalSignals;
    int               executedTrades;
    int               winningTrades;
    double            totalProfit;
    double            maxDrawdown;
    double            sharpeRatio;
    double            winRate;
    datetime          lastSignalTime;
};

//+------------------------------------------------------------------+
//| HFT Strategies Class                                            |
//+------------------------------------------------------------------+
class CHFTStrategies
{
private:
    // Configuration
    SStrategyConfig   m_config;
    
    // Strategy performance tracking
    SStrategyPerformance m_arbitragePerf;
    SStrategyPerformance m_marketMakingPerf;
    SStrategyPerformance m_momentumPerf;
    SStrategyPerformance m_scalpingPerf;
    
    // References to other components
    CHFTTradingEngine *m_tradingEngine;
    CHFTRiskManager   *m_riskManager;
    
    // Strategy state variables
    datetime          m_lastArbitrageCheck;
    datetime          m_lastMarketMakingUpdate;
    datetime          m_lastMomentumSignal;
    datetime          m_lastScalpingSignal;
    
    // Market making state
    bool              m_quotesActive;
    double            m_currentBidQuote;
    double            m_currentAskQuote;
    ulong             m_bidOrderTicket;
    ulong             m_askOrderTicket;
    
    // Momentum state
    double            m_lastMomentumValue;
    bool              m_momentumTrendUp;
    
    // Scalping state
    double            m_scalpingEntryPrice;
    bool              m_scalpingPositionOpen;

public:
    // Constructor/Destructor
                     CHFTStrategies();
                    ~CHFTStrategies();
    
    // Initialization
    bool              Initialize(SStrategyConfig &config);
    void              SetTradingEngine(CHFTTradingEngine *engine);
    void              SetRiskManager(CHFTRiskManager *riskManager);
    
    // Strategy execution methods
    void              ExecuteArbitrage(SMarketAnalysis &analysis);
    void              ExecuteMarketMaking(SMarketAnalysis &analysis);
    void              ExecuteMomentum(SMarketAnalysis &analysis);
    void              ExecuteScalping(SMarketAnalysis &analysis);

    // Missing method declarations
    bool              CheckStatisticalArbitrage(SMarketAnalysis &analysis);
    bool              ExecuteArbitrageSignal(double expectedProfit, ENUM_ORDER_TYPE orderType);
    void              ManageMarketMakingRisk(SMarketAnalysis &analysis);
    bool              DetectTrendReversal(SMarketAnalysis &analysis);
    double            CalculateMomentumStrength(SMarketAnalysis &analysis);
    void              ManageScalpingPosition(SMarketAnalysis &analysis);
    bool              CheckScalpingExit(SMarketAnalysis &analysis);
    
    // Arbitrage strategies
    bool              CheckPriceArbitrage(SMarketAnalysis &analysis);
    bool              CheckStatisticalArbitrage(SMarketAnalysis &analysis);
    bool              ExecuteArbitrageSignal(double expectedProfit, ENUM_ORDER_TYPE orderType);
    
    // Market making strategies
    void              UpdateMarketMakingQuotes(SMarketAnalysis &analysis);
    double            CalculateOptimalBidPrice(SMarketAnalysis &analysis);
    double            CalculateOptimalAskPrice(SMarketAnalysis &analysis);
    void              ManageMarketMakingRisk(SMarketAnalysis &analysis);
    
    // Momentum strategies
    bool              DetectMomentumBreakout(SMarketAnalysis &analysis);
    bool              DetectTrendReversal(SMarketAnalysis &analysis);
    double            CalculateMomentumStrength(SMarketAnalysis &analysis);
    
    // Scalping strategies
    bool              DetectScalpingOpportunity(SMarketAnalysis &analysis);
    void              ManageScalpingPosition(SMarketAnalysis &analysis);
    bool              CheckScalpingExit(SMarketAnalysis &analysis);
    
    // Signal generation
    STradeSignal      GenerateArbitrageSignal(SMarketAnalysis &analysis, double expectedProfit);
    STradeSignal      GenerateMarketMakingSignal(SMarketAnalysis &analysis, bool isBid);
    STradeSignal      GenerateMomentumSignal(SMarketAnalysis &analysis, ENUM_ORDER_TYPE direction);
    STradeSignal      GenerateScalpingSignal(SMarketAnalysis &analysis, ENUM_ORDER_TYPE direction);
    
    // Performance tracking
    void              UpdateStrategyPerformance(string strategyName, bool wasWinning, double profit);
    SStrategyPerformance GetStrategyPerformance(string strategyName);
    string            GetPerformanceReport();
    
    // Utility methods
    bool              IsStrategyEnabled(string strategyName);
    void              EnableStrategy(string strategyName, bool enable);
    double            CalculateSignalConfidence(SMarketAnalysis &analysis, string strategyType);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CHFTStrategies::CHFTStrategies()
{
    m_tradingEngine = NULL;
    m_riskManager = NULL;
    
    m_lastArbitrageCheck = 0;
    m_lastMarketMakingUpdate = 0;
    m_lastMomentumSignal = 0;
    m_lastScalpingSignal = 0;
    
    m_quotesActive = false;
    m_currentBidQuote = 0;
    m_currentAskQuote = 0;
    m_bidOrderTicket = 0;
    m_askOrderTicket = 0;
    
    m_lastMomentumValue = 0;
    m_momentumTrendUp = false;
    
    m_scalpingEntryPrice = 0;
    m_scalpingPositionOpen = false;
    
    // Initialize performance structures
    ZeroMemory(m_arbitragePerf);
    ZeroMemory(m_marketMakingPerf);
    ZeroMemory(m_momentumPerf);
    ZeroMemory(m_scalpingPerf);
    
    m_arbitragePerf.strategyName = "Arbitrage";
    m_marketMakingPerf.strategyName = "Market Making";
    m_momentumPerf.strategyName = "Momentum";
    m_scalpingPerf.strategyName = "Scalping";
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CHFTStrategies::~CHFTStrategies()
{
}

//+------------------------------------------------------------------+
//| Initialize strategies                                           |
//+------------------------------------------------------------------+
bool CHFTStrategies::Initialize(SStrategyConfig &config)
{
    m_config = config;
    
    Print("HFT Strategies initialized:");
    Print("- Arbitrage: ", m_config.enableArbitrage ? "ON" : "OFF");
    Print("- Market Making: ", m_config.enableMarketMaking ? "ON" : "OFF");
    Print("- Momentum: ", m_config.enableMomentum ? "ON" : "OFF");
    Print("- Scalping: ", m_config.enableScalping ? "ON" : "OFF");
    
    return true;
}

//+------------------------------------------------------------------+
//| Set trading engine reference                                   |
//+------------------------------------------------------------------+
void CHFTStrategies::SetTradingEngine(CHFTTradingEngine *engine)
{
    m_tradingEngine = engine;
}

//+------------------------------------------------------------------+
//| Set risk manager reference                                      |
//+------------------------------------------------------------------+
void CHFTStrategies::SetRiskManager(CHFTRiskManager *riskManager)
{
    m_riskManager = riskManager;
}

//+------------------------------------------------------------------+
//| Execute arbitrage strategy                                      |
//+------------------------------------------------------------------+
void CHFTStrategies::ExecuteArbitrage(SMarketAnalysis &analysis)
{
    if(!m_config.enableArbitrage) return;
    
    datetime currentTime = TimeCurrent();
    if(currentTime - m_lastArbitrageCheck < 1) return; // Check every second
    
    m_lastArbitrageCheck = currentTime;
    
    // Check for price arbitrage opportunities
    if(CheckPriceArbitrage(analysis))
    {
        Print("Price arbitrage opportunity detected");
        m_arbitragePerf.totalSignals++;
    }
    
    // Check for statistical arbitrage
    if(CheckStatisticalArbitrage(analysis))
    {
        Print("Statistical arbitrage opportunity detected");
        m_arbitragePerf.totalSignals++;
    }
}

//+------------------------------------------------------------------+
//| Execute market making strategy                                  |
//+------------------------------------------------------------------+
void CHFTStrategies::ExecuteMarketMaking(SMarketAnalysis &analysis)
{
    if(!m_config.enableMarketMaking) return;
    
    datetime currentTime = TimeCurrent();
    if(currentTime - m_lastMarketMakingUpdate < 5) return; // Update every 5 seconds
    
    m_lastMarketMakingUpdate = currentTime;
    
    // Update quotes based on market conditions
    UpdateMarketMakingQuotes(analysis);
    
    // Manage risk
    ManageMarketMakingRisk(analysis);
    
    m_marketMakingPerf.totalSignals++;
}

//+------------------------------------------------------------------+
//| Execute momentum strategy                                       |
//+------------------------------------------------------------------+
void CHFTStrategies::ExecuteMomentum(SMarketAnalysis &analysis)
{
    if(!m_config.enableMomentum) return;
    
    // Detect momentum breakouts
    if(DetectMomentumBreakout(analysis))
    {
        ENUM_ORDER_TYPE direction = (analysis.momentum > 0) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
        STradeSignal signal = GenerateMomentumSignal(analysis, direction);
        
        if(signal.isValid && m_tradingEngine != NULL)
        {
            if(m_tradingEngine.ExecuteSignal(signal))
            {
                m_momentumPerf.executedTrades++;
                m_lastMomentumSignal = TimeCurrent();
            }
        }
        m_momentumPerf.totalSignals++;
    }
    
    // Check for trend reversals
    if(DetectTrendReversal(analysis))
    {
        Print("Momentum trend reversal detected");
    }
}

//+------------------------------------------------------------------+
//| Execute scalping strategy                                       |
//+------------------------------------------------------------------+
void CHFTStrategies::ExecuteScalping(SMarketAnalysis &analysis)
{
    if(!m_config.enableScalping) return;
    
    // Manage existing scalping position
    if(m_scalpingPositionOpen)
    {
        ManageScalpingPosition(analysis);
        return;
    }
    
    // Look for new scalping opportunities
    if(DetectScalpingOpportunity(analysis))
    {
        ENUM_ORDER_TYPE direction = (analysis.tickDirection > 0) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
        STradeSignal signal = GenerateScalpingSignal(analysis, direction);
        
        if(signal.isValid && m_tradingEngine != NULL)
        {
            if(m_tradingEngine.ExecuteSignal(signal))
            {
                m_scalpingPositionOpen = true;
                m_scalpingEntryPrice = (direction == ORDER_TYPE_BUY) ? analysis.ask : analysis.bid;
                m_scalpingPerf.executedTrades++;
                m_lastScalpingSignal = TimeCurrent();
            }
        }
        m_scalpingPerf.totalSignals++;
    }
}

//+------------------------------------------------------------------+
//| Check for price arbitrage                                      |
//+------------------------------------------------------------------+
bool CHFTStrategies::CheckPriceArbitrage(SMarketAnalysis &analysis)
{
    // Simplified arbitrage check - would need multiple data feeds in practice
    double spreadThreshold = m_config.minArbitrageSpread * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    
    if(analysis.spread < spreadThreshold && analysis.isLiquid)
    {
        // Potential arbitrage opportunity
        double expectedProfit = spreadThreshold - analysis.spread;
        return ExecuteArbitrageSignal(expectedProfit, ORDER_TYPE_BUY);
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| Update market making quotes                                     |
//+------------------------------------------------------------------+
void CHFTStrategies::UpdateMarketMakingQuotes(SMarketAnalysis &analysis)
{
    if(!analysis.isLiquid) return;
    
    double optimalBid = CalculateOptimalBidPrice(analysis);
    double optimalAsk = CalculateOptimalAskPrice(analysis);
    
    // Check if quotes need updating
    bool updateNeeded = false;
    if(MathAbs(optimalBid - m_currentBidQuote) > SymbolInfoDouble(Symbol(), SYMBOL_POINT))
        updateNeeded = true;
    if(MathAbs(optimalAsk - m_currentAskQuote) > SymbolInfoDouble(Symbol(), SYMBOL_POINT))
        updateNeeded = true;
    
    if(updateNeeded && m_tradingEngine != NULL)
    {
        // Cancel existing quotes
        if(m_quotesActive)
        {
            // Cancel previous orders (simplified)
            m_quotesActive = false;
        }
        
        // Place new quotes
        double volume = 0.01; // Minimum volume for market making
        if(m_tradingEngine.PlaceQuotes(optimalBid, optimalAsk, volume))
        {
            m_currentBidQuote = optimalBid;
            m_currentAskQuote = optimalAsk;
            m_quotesActive = true;
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate optimal bid price                                     |
//+------------------------------------------------------------------+
double CHFTStrategies::CalculateOptimalBidPrice(SMarketAnalysis &analysis)
{
    double midPrice = (analysis.bid + analysis.ask) / 2.0;
    double halfSpread = (analysis.spread * m_config.spreadMultiplier) / 2.0;
    
    // Adjust for market imbalance
    double imbalanceAdjustment = analysis.imbalance * halfSpread * 0.5;
    
    return midPrice - halfSpread + imbalanceAdjustment;
}

//+------------------------------------------------------------------+
//| Calculate optimal ask price                                     |
//+------------------------------------------------------------------+
double CHFTStrategies::CalculateOptimalAskPrice(SMarketAnalysis &analysis)
{
    double midPrice = (analysis.bid + analysis.ask) / 2.0;
    double halfSpread = (analysis.spread * m_config.spreadMultiplier) / 2.0;
    
    // Adjust for market imbalance
    double imbalanceAdjustment = analysis.imbalance * halfSpread * 0.5;
    
    return midPrice + halfSpread + imbalanceAdjustment;
}

//+------------------------------------------------------------------+
//| Detect momentum breakout                                        |
//+------------------------------------------------------------------+
bool CHFTStrategies::DetectMomentumBreakout(SMarketAnalysis &analysis)
{
    // Check momentum threshold
    if(MathAbs(analysis.momentum) < m_config.momentumThreshold)
        return false;
    
    // Check moving average crossover
    bool maCrossover = false;
    if(analysis.sma5 > analysis.sma20 && analysis.momentum > 0)
        maCrossover = true;
    else if(analysis.sma5 < analysis.sma20 && analysis.momentum < 0)
        maCrossover = true;
    
    // Check volume confirmation
    bool volumeConfirm = analysis.volume > 1000; // Minimum volume threshold
    
    return (maCrossover && volumeConfirm);
}

//+------------------------------------------------------------------+
//| Detect scalping opportunity                                     |
//+------------------------------------------------------------------+
bool CHFTStrategies::DetectScalpingOpportunity(SMarketAnalysis &analysis)
{
    // Check market conditions
    if(!analysis.isLiquid || analysis.isVolatile)
        return false;
    
    // Check spread
    if(analysis.spread > 2.0 * SymbolInfoDouble(Symbol(), SYMBOL_POINT))
        return false;
    
    // Check tick direction consistency
    if(MathAbs(analysis.tickDirection) < 0.5)
        return false;
    
    // Check RSI for overbought/oversold
    if(analysis.rsi > 70 && analysis.tickDirection < 0)
        return true; // Sell signal
    if(analysis.rsi < 30 && analysis.tickDirection > 0)
        return true; // Buy signal
    
    return false;
}

//+------------------------------------------------------------------+
//| Generate momentum signal                                        |
//+------------------------------------------------------------------+
STradeSignal CHFTStrategies::GenerateMomentumSignal(SMarketAnalysis &analysis, ENUM_ORDER_TYPE direction)
{
    STradeSignal signal = {};
    
    signal.orderType = direction;
    signal.volume = 0.01; // Base volume
    signal.confidence = CalculateSignalConfidence(analysis, "Momentum");
    signal.strategy = "Momentum";
    signal.isValid = true;
    signal.comment = "HFT_Momentum";
    
    // Set price based on direction
    if(direction == ORDER_TYPE_BUY)
    {
        signal.price = analysis.ask;
        signal.stopLoss = analysis.bid - (m_config.scalpingStop * SymbolInfoDouble(Symbol(), SYMBOL_POINT));
        signal.takeProfit = analysis.ask + (m_config.scalpingTarget * SymbolInfoDouble(Symbol(), SYMBOL_POINT));
    }
    else
    {
        signal.price = analysis.bid;
        signal.stopLoss = analysis.ask + (m_config.scalpingStop * SymbolInfoDouble(Symbol(), SYMBOL_POINT));
        signal.takeProfit = analysis.bid - (m_config.scalpingTarget * SymbolInfoDouble(Symbol(), SYMBOL_POINT));
    }
    
    return signal;
}

//+------------------------------------------------------------------+
//| Generate scalping signal                                        |
//+------------------------------------------------------------------+
STradeSignal CHFTStrategies::GenerateScalpingSignal(SMarketAnalysis &analysis, ENUM_ORDER_TYPE direction)
{
    STradeSignal signal = {};
    
    signal.orderType = direction;
    signal.volume = 0.01;
    signal.confidence = CalculateSignalConfidence(analysis, "Scalping");
    signal.strategy = "Scalping";
    signal.isValid = true;
    signal.comment = "HFT_Scalping";
    
    // Set tight stops and targets for scalping
    if(direction == ORDER_TYPE_BUY)
    {
        signal.price = analysis.ask;
        signal.stopLoss = analysis.ask - (m_config.scalpingStop * SymbolInfoDouble(Symbol(), SYMBOL_POINT));
        signal.takeProfit = analysis.ask + (m_config.scalpingTarget * SymbolInfoDouble(Symbol(), SYMBOL_POINT));
    }
    else
    {
        signal.price = analysis.bid;
        signal.stopLoss = analysis.bid + (m_config.scalpingStop * SymbolInfoDouble(Symbol(), SYMBOL_POINT));
        signal.takeProfit = analysis.bid - (m_config.scalpingTarget * SymbolInfoDouble(Symbol(), SYMBOL_POINT));
    }
    
    return signal;
}

//+------------------------------------------------------------------+
//| Calculate signal confidence                                     |
//+------------------------------------------------------------------+
double CHFTStrategies::CalculateSignalConfidence(SMarketAnalysis &analysis, string strategyType)
{
    double confidence = 0.5; // Base confidence
    
    // Adjust based on market conditions
    if(analysis.isLiquid) confidence += 0.1;
    if(!analysis.isVolatile) confidence += 0.1;
    
    // Strategy-specific adjustments
    if(strategyType == "Momentum")
    {
        confidence += MathAbs(analysis.momentum) * 100; // Scale momentum
        if(analysis.volume > 1000) confidence += 0.1;
    }
    else if(strategyType == "Scalping")
    {
        if(analysis.spread < SymbolInfoDouble(Symbol(), SYMBOL_POINT)) confidence += 0.2;
        confidence += MathAbs(analysis.tickDirection) * 0.1;
    }
    
    return MathMin(1.0, MathMax(0.0, confidence));
}

//+------------------------------------------------------------------+
//| Get performance report                                          |
//+------------------------------------------------------------------+
string CHFTStrategies::GetPerformanceReport()
{
    string report = "=== STRATEGY PERFORMANCE ===\n";
    
    report += StringFormat("Arbitrage: %d signals, %d trades\n", 
                          m_arbitragePerf.totalSignals, m_arbitragePerf.executedTrades);
    report += StringFormat("Market Making: %d signals, %d trades\n", 
                          m_marketMakingPerf.totalSignals, m_marketMakingPerf.executedTrades);
    report += StringFormat("Momentum: %d signals, %d trades\n", 
                          m_momentumPerf.totalSignals, m_momentumPerf.executedTrades);
    report += StringFormat("Scalping: %d signals, %d trades\n", 
                          m_scalpingPerf.totalSignals, m_scalpingPerf.executedTrades);
    
    return report;
}

//+------------------------------------------------------------------+
//| Check for statistical arbitrage                                |
//+------------------------------------------------------------------+
bool CHFTStrategies::CheckStatisticalArbitrage(SMarketAnalysis &analysis)
{
    // Simplified statistical arbitrage check
    if(analysis.rsi > 80 || analysis.rsi < 20)
    {
        double expectedProfit = MathAbs(analysis.rsi - 50) * 0.001;
        ENUM_ORDER_TYPE direction = (analysis.rsi > 80) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
        return ExecuteArbitrageSignal(expectedProfit, direction);
    }
    return false;
}

//+------------------------------------------------------------------+
//| Execute arbitrage signal                                       |
//+------------------------------------------------------------------+
bool CHFTStrategies::ExecuteArbitrageSignal(double expectedProfit, ENUM_ORDER_TYPE orderType)
{
    if(expectedProfit < m_config.minArbitrageSpread * SymbolInfoDouble(Symbol(), SYMBOL_POINT))
        return false;

    STradeSignal signal = GenerateArbitrageSignal(SMarketAnalysis(), expectedProfit);
    signal.orderType = orderType;

    if(m_tradingEngine != NULL && signal.isValid)
    {
        return m_tradingEngine.ExecuteSignal(signal);
    }

    return false;
}

//+------------------------------------------------------------------+
//| Generate arbitrage signal                                      |
//+------------------------------------------------------------------+
STradeSignal CHFTStrategies::GenerateArbitrageSignal(SMarketAnalysis &analysis, double expectedProfit)
{
    STradeSignal signal = {};

    signal.orderType = ORDER_TYPE_BUY; // Will be overridden
    signal.volume = 0.01;
    signal.confidence = expectedProfit * 1000; // Scale expected profit
    signal.strategy = "Arbitrage";
    signal.isValid = true;
    signal.comment = "HFT_Arbitrage";

    return signal;
}

//+------------------------------------------------------------------+
//| Generate market making signal                                  |
//+------------------------------------------------------------------+
STradeSignal CHFTStrategies::GenerateMarketMakingSignal(SMarketAnalysis &analysis, bool isBid)
{
    STradeSignal signal = {};

    signal.orderType = isBid ? ORDER_TYPE_BUY_LIMIT : ORDER_TYPE_SELL_LIMIT;
    signal.volume = 0.01;
    signal.confidence = 0.7; // Market making has moderate confidence
    signal.strategy = "MarketMaking";
    signal.isValid = true;
    signal.comment = "HFT_MM";

    if(isBid)
        signal.price = CalculateOptimalBidPrice(analysis);
    else
        signal.price = CalculateOptimalAskPrice(analysis);

    return signal;
}

//+------------------------------------------------------------------+
//| Manage market making risk                                      |
//+------------------------------------------------------------------+
void CHFTStrategies::ManageMarketMakingRisk(SMarketAnalysis &analysis)
{
    if(!m_quotesActive) return;

    // Cancel quotes if spread becomes too wide
    if(analysis.spread > 5.0 * SymbolInfoDouble(Symbol(), SYMBOL_POINT))
    {
        m_quotesActive = false;
        Print("Market making quotes cancelled due to wide spread");
    }

    // Cancel quotes if volatility is too high
    if(analysis.isVolatile)
    {
        m_quotesActive = false;
        Print("Market making quotes cancelled due to high volatility");
    }
}

//+------------------------------------------------------------------+
//| Detect trend reversal                                          |
//+------------------------------------------------------------------+
bool CHFTStrategies::DetectTrendReversal(SMarketAnalysis &analysis)
{
    // Simple reversal detection based on momentum change
    bool momentumReversal = false;

    if(m_lastMomentumValue > 0 && analysis.momentum < 0)
        momentumReversal = true;
    else if(m_lastMomentumValue < 0 && analysis.momentum > 0)
        momentumReversal = true;

    m_lastMomentumValue = analysis.momentum;

    return momentumReversal;
}

//+------------------------------------------------------------------+
//| Calculate momentum strength                                     |
//+------------------------------------------------------------------+
double CHFTStrategies::CalculateMomentumStrength(SMarketAnalysis &analysis)
{
    double strength = MathAbs(analysis.momentum) * 1000; // Scale momentum

    // Adjust for volume
    if(analysis.volume > 1000)
        strength *= 1.2;

    // Adjust for volatility
    if(analysis.isVolatile)
        strength *= 0.8;

    return MathMin(1.0, strength);
}

//+------------------------------------------------------------------+
//| Manage scalping position                                       |
//+------------------------------------------------------------------+
void CHFTStrategies::ManageScalpingPosition(SMarketAnalysis &analysis)
{
    if(!m_scalpingPositionOpen) return;

    // Check for exit conditions
    if(CheckScalpingExit(analysis))
    {
        // Close position logic would go here
        m_scalpingPositionOpen = false;
        Print("Scalping position closed");
    }
}

//+------------------------------------------------------------------+
//| Check scalping exit conditions                                 |
//+------------------------------------------------------------------+
bool CHFTStrategies::CheckScalpingExit(SMarketAnalysis &analysis)
{
    if(!m_scalpingPositionOpen) return false;

    // Time-based exit (hold for maximum 1 minute)
    if(TimeCurrent() - m_lastScalpingSignal > 60)
        return true;

    // Profit target reached
    double currentPrice = (analysis.bid + analysis.ask) / 2.0;
    double priceDiff = MathAbs(currentPrice - m_scalpingEntryPrice);

    if(priceDiff >= m_config.scalpingTarget * SymbolInfoDouble(Symbol(), SYMBOL_POINT))
        return true;

    // Stop loss hit
    if(priceDiff >= m_config.scalpingStop * SymbolInfoDouble(Symbol(), SYMBOL_POINT))
        return true;

    return false;
}
