//+------------------------------------------------------------------+
//|                                           HFT_MarketData.mqh |
//|                                    High-Performance Market Data |
//+------------------------------------------------------------------+

#include <Trade\SymbolInfo.mqh>

//+------------------------------------------------------------------+
//| Market Analysis Structure                                        |
//+------------------------------------------------------------------+
struct SMarketAnalysis
{
    // Price data
    double            bid;
    double            ask;
    double            spread;
    double            last;
    
    // Volume data
    long              volume;
    long              volumeReal;
    
    // Volatility measures
    double            atr;
    double            volatility;
    
    // Momentum indicators
    double            momentum;
    double            rsi;
    double            macd;
    double            macdSignal;
    
    // Moving averages
    double            sma5;
    double            sma20;
    double            ema5;
    double            ema20;
    
    // Market microstructure
    double            bidSize;
    double            askSize;
    double            imbalance;
    double            tickDirection;
    
    // Time-based
    datetime          timestamp;
    int               milliseconds;
    
    // Market state
    bool              isLiquid;
    bool              isTrending;
    bool              isVolatile;
    ENUM_TIMEFRAMES   dominantTimeframe;
};

//+------------------------------------------------------------------+
//| Tick Data Structure                                             |
//+------------------------------------------------------------------+
struct STickData
{
    datetime          time;
    double            bid;
    double            ask;
    double            last;
    ulong             volume;
    long              time_msc;
    uint              flags;
    double            volume_real;
};

//+------------------------------------------------------------------+
//| Market Data Handler Class                                       |
//+------------------------------------------------------------------+
class CHFTMarketData
{
private:
    // Symbol information
    string            m_symbol;
    CSymbolInfo       m_symbolInfo;
    
    // Tick data storage
    STickData         m_ticks[];
    int               m_tickCount;
    int               m_maxTicks;
    
    // Price arrays for calculations
    double            m_prices[];
    double            m_volumes[];
    int               m_priceCount;
    
    // Market analysis cache
    SMarketAnalysis   m_analysis;
    datetime          m_lastAnalysisTime;
    
    // Performance optimization
    bool              m_fastMode;
    int               m_updateInterval;
    datetime          m_lastUpdate;
    
    // Market microstructure
    double            m_lastBid;
    double            m_lastAsk;
    double            m_tickDirection;
    double            m_bidAskImbalance;
    
    // Volatility calculation
    double            m_volatilityBuffer[];
    int               m_volatilityPeriod;

public:
    // Constructor/Destructor
                     CHFTMarketData();
                    ~CHFTMarketData();
    
    // Initialization
    bool              Initialize(string symbol, int maxTicks = 1000);
    
    // Data update methods
    bool              UpdateTick();
    void              UpdateAnalysis();
    void              CalculateIndicators();
    
    // Market microstructure analysis
    void              AnalyzeMicrostructure();
    double            CalculateImbalance();
    double            GetTickDirection();
    
    // Technical indicators
    double            CalculateATR(int period = 14);
    double            CalculateVolatility(int period = 20);
    double            CalculateRSI(int period = 14);
    double            CalculateMACD(int fastPeriod = 12, int slowPeriod = 26, int signalPeriod = 9);
    double            CalculateSMA(int period);
    double            CalculateEMA(int period);
    
    // Market state detection
    bool              IsMarketLiquid();
    bool              IsMarketTrending();
    bool              IsMarketVolatile();
    ENUM_TIMEFRAMES   GetDominantTimeframe();
    
    // Data access
    SMarketAnalysis   GetAnalysis() { return m_analysis; }
    STickData         GetLastTick();
    double            GetSpread() { return m_symbolInfo.Spread() * m_symbolInfo.Point(); }
    double            GetBid() { return m_symbolInfo.Bid(); }
    double            GetAsk() { return m_symbolInfo.Ask(); }
    
    // Utility methods
    void              SetFastMode(bool enabled) { m_fastMode = enabled; }
    void              SetUpdateInterval(int interval) { m_updateInterval = interval; }
    string            GetMarketStateString();
    
    // Data export
    bool              ExportTickData(string filename);
    void              PrintMarketSummary();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CHFTMarketData::CHFTMarketData()
{
    m_symbol = "";
    m_tickCount = 0;
    m_maxTicks = 1000;
    m_priceCount = 0;
    m_lastAnalysisTime = 0;
    m_fastMode = true;
    m_updateInterval = 1; // 1 second
    m_lastUpdate = 0;
    m_lastBid = 0;
    m_lastAsk = 0;
    m_tickDirection = 0;
    m_bidAskImbalance = 0;
    m_volatilityPeriod = 20;
    
    // Initialize analysis structure
    ZeroMemory(m_analysis);
}

//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
CHFTMarketData::~CHFTMarketData()
{
}

//+------------------------------------------------------------------+
//| Initialize market data handler                                  |
//+------------------------------------------------------------------+
bool CHFTMarketData::Initialize(string symbol, int maxTicks = 1000)
{
    m_symbol = symbol;
    m_maxTicks = maxTicks;
    
    if(!m_symbolInfo.Name(symbol))
    {
        Print("Error: Failed to initialize symbol info for ", symbol);
        return false;
    }
    
    // Resize arrays
    ArrayResize(m_ticks, m_maxTicks);
    ArrayResize(m_prices, m_maxTicks);
    ArrayResize(m_volumes, m_maxTicks);
    ArrayResize(m_volatilityBuffer, m_volatilityPeriod);
    
    // Initialize with current market data
    UpdateTick();
    
    Print("Market Data Handler initialized for ", symbol);
    Print("- Max ticks storage: ", m_maxTicks);
    Print("- Fast mode: ", m_fastMode ? "ON" : "OFF");
    
    return true;
}

//+------------------------------------------------------------------+
//| Update tick data                                                |
//+------------------------------------------------------------------+
bool CHFTMarketData::UpdateTick()
{
    if(!m_symbolInfo.RefreshRates())
        return false;
    
    // Get current tick
    MqlTick tick;
    if(!SymbolInfoTick(m_symbol, tick))
        return false;
    
    // Store tick data
    if(m_tickCount >= m_maxTicks)
    {
        // Shift array to make room for new tick
        for(int i = 0; i < m_maxTicks - 1; i++)
            m_ticks[i] = m_ticks[i + 1];
        m_tickCount = m_maxTicks - 1;
    }
    
    m_ticks[m_tickCount].time = tick.time;
    m_ticks[m_tickCount].bid = tick.bid;
    m_ticks[m_tickCount].ask = tick.ask;
    m_ticks[m_tickCount].last = tick.last;
    m_ticks[m_tickCount].volume = tick.volume;
    m_ticks[m_tickCount].time_msc = tick.time_msc;
    m_ticks[m_tickCount].flags = tick.flags;
    m_ticks[m_tickCount].volume_real = tick.volume_real;
    
    m_tickCount++;
    
    // Update analysis if enough time has passed
    datetime currentTime = TimeCurrent();
    if(currentTime - m_lastUpdate >= m_updateInterval)
    {
        UpdateAnalysis();
        m_lastUpdate = currentTime;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update market analysis                                          |
//+------------------------------------------------------------------+
void CHFTMarketData::UpdateAnalysis()
{
    if(m_tickCount < 10) return; // Need minimum data
    
    // Update basic price data
    m_analysis.bid = m_symbolInfo.Bid();
    m_analysis.ask = m_symbolInfo.Ask();
    m_analysis.spread = GetSpread();
    m_analysis.last = m_symbolInfo.Last();
    m_analysis.timestamp = TimeCurrent();
    
    // Calculate indicators
    CalculateIndicators();
    
    // Analyze microstructure
    AnalyzeMicrostructure();
    
    // Determine market state
    m_analysis.isLiquid = IsMarketLiquid();
    m_analysis.isTrending = IsMarketTrending();
    m_analysis.isVolatile = IsMarketVolatile();
    m_analysis.dominantTimeframe = GetDominantTimeframe();
    
    m_lastAnalysisTime = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Calculate technical indicators                                  |
//+------------------------------------------------------------------+
void CHFTMarketData::CalculateIndicators()
{
    if(m_tickCount < 20) return;
    
    // Prepare price array
    for(int i = 0; i < MathMin(m_tickCount, 100); i++)
    {
        m_prices[i] = (m_ticks[i].bid + m_ticks[i].ask) / 2.0;
    }
    m_priceCount = MathMin(m_tickCount, 100);
    
    // Calculate moving averages
    m_analysis.sma5 = CalculateSMA(5);
    m_analysis.sma20 = CalculateSMA(20);
    m_analysis.ema5 = CalculateEMA(5);
    m_analysis.ema20 = CalculateEMA(20);
    
    // Calculate momentum indicators
    m_analysis.atr = CalculateATR(14);
    m_analysis.volatility = CalculateVolatility(20);
    m_analysis.rsi = CalculateRSI(14);
    m_analysis.macd = CalculateMACD(12, 26, 9);
    
    // Calculate momentum
    if(m_priceCount >= 10)
    {
        double currentPrice = m_prices[m_priceCount - 1];
        double pastPrice = m_prices[m_priceCount - 10];
        m_analysis.momentum = (currentPrice - pastPrice) / pastPrice;
    }
}

//+------------------------------------------------------------------+
//| Analyze market microstructure                                  |
//+------------------------------------------------------------------+
void CHFTMarketData::AnalyzeMicrostructure()
{
    if(m_tickCount < 2) return;
    
    // Calculate tick direction
    m_analysis.tickDirection = GetTickDirection();
    
    // Calculate bid-ask imbalance
    m_analysis.imbalance = CalculateImbalance();
    
    // Estimate order book depth (simplified)
    m_analysis.bidSize = m_symbolInfo.Volume();
    m_analysis.askSize = m_symbolInfo.Volume();
}

//+------------------------------------------------------------------+
//| Calculate bid-ask imbalance                                     |
//+------------------------------------------------------------------+
double CHFTMarketData::CalculateImbalance()
{
    if(m_tickCount < 10) return 0;
    
    double bidVolume = 0;
    double askVolume = 0;
    
    for(int i = m_tickCount - 10; i < m_tickCount; i++)
    {
        if(i > 0)
        {
            if(m_ticks[i].last > m_ticks[i-1].last)
                askVolume += (double)m_ticks[i].volume_real;
            else if(m_ticks[i].last < m_ticks[i-1].last)
                bidVolume += (double)m_ticks[i].volume_real;
        }
    }
    
    double totalVolume = bidVolume + askVolume;
    if(totalVolume > 0)
        return (askVolume - bidVolume) / totalVolume;
    
    return 0;
}

//+------------------------------------------------------------------+
//| Get tick direction                                              |
//+------------------------------------------------------------------+
double CHFTMarketData::GetTickDirection()
{
    if(m_tickCount < 2) return 0;
    
    double currentPrice = (m_ticks[m_tickCount-1].bid + m_ticks[m_tickCount-1].ask) / 2.0;
    double previousPrice = (m_ticks[m_tickCount-2].bid + m_ticks[m_tickCount-2].ask) / 2.0;
    
    if(currentPrice > previousPrice) return 1.0;
    else if(currentPrice < previousPrice) return -1.0;
    else return 0.0;
}

//+------------------------------------------------------------------+
//| Calculate Simple Moving Average                                 |
//+------------------------------------------------------------------+
double CHFTMarketData::CalculateSMA(int period)
{
    if(m_priceCount < period) return 0;
    
    double sum = 0;
    for(int i = m_priceCount - period; i < m_priceCount; i++)
        sum += m_prices[i];
    
    return sum / period;
}

//+------------------------------------------------------------------+
//| Calculate Exponential Moving Average                            |
//+------------------------------------------------------------------+
double CHFTMarketData::CalculateEMA(int period)
{
    if(m_priceCount < period) return 0;
    
    double multiplier = 2.0 / (period + 1);
    double ema = m_prices[m_priceCount - period];
    
    for(int i = m_priceCount - period + 1; i < m_priceCount; i++)
        ema = (m_prices[i] * multiplier) + (ema * (1 - multiplier));
    
    return ema;
}

//+------------------------------------------------------------------+
//| Calculate ATR                                                   |
//+------------------------------------------------------------------+
double CHFTMarketData::CalculateATR(int period = 14)
{
    if(m_tickCount < period + 1) return 0;
    
    double atr = 0;
    for(int i = m_tickCount - period; i < m_tickCount - 1; i++)
    {
        double high = MathMax(m_ticks[i].bid, m_ticks[i].ask);
        double low = MathMin(m_ticks[i].bid, m_ticks[i].ask);
        double prevClose = (m_ticks[i-1].bid + m_ticks[i-1].ask) / 2.0;
        
        double tr = MathMax(high - low, MathMax(MathAbs(high - prevClose), MathAbs(low - prevClose)));
        atr += tr;
    }
    
    return atr / period;
}

//+------------------------------------------------------------------+
//| Calculate volatility                                            |
//+------------------------------------------------------------------+
double CHFTMarketData::CalculateVolatility(int period = 20)
{
    if(m_priceCount < period) return 0;
    
    double mean = CalculateSMA(period);
    double variance = 0;
    
    for(int i = m_priceCount - period; i < m_priceCount; i++)
    {
        double diff = m_prices[i] - mean;
        variance += diff * diff;
    }
    
    return MathSqrt(variance / period);
}

//+------------------------------------------------------------------+
//| Check if market is liquid                                       |
//+------------------------------------------------------------------+
bool CHFTMarketData::IsMarketLiquid()
{
    double spread = GetSpread();
    double avgSpread = m_symbolInfo.Point() * 2; // Typical spread threshold
    
    return (spread <= avgSpread && m_symbolInfo.Volume() > 100);
}

//+------------------------------------------------------------------+
//| Check if market is trending                                     |
//+------------------------------------------------------------------+
bool CHFTMarketData::IsMarketTrending()
{
    return (MathAbs(m_analysis.momentum) > 0.001 && 
            ((m_analysis.sma5 > m_analysis.sma20 && m_analysis.momentum > 0) ||
             (m_analysis.sma5 < m_analysis.sma20 && m_analysis.momentum < 0)));
}

//+------------------------------------------------------------------+
//| Check if market is volatile                                     |
//+------------------------------------------------------------------+
bool CHFTMarketData::IsMarketVolatile()
{
    double avgVolatility = m_symbolInfo.Point() * 10; // Threshold
    return (m_analysis.volatility > avgVolatility);
}

//+------------------------------------------------------------------+
//| Get market state as string                                      |
//+------------------------------------------------------------------+
string CHFTMarketData::GetMarketStateString()
{
    string state = "";
    if(m_analysis.isLiquid) state += "LIQUID ";
    if(m_analysis.isTrending) state += "TRENDING ";
    if(m_analysis.isVolatile) state += "VOLATILE ";
    
    if(state == "") state = "NORMAL";
    
    return state;
}

//+------------------------------------------------------------------+
//| Print market summary                                            |
//+------------------------------------------------------------------+
void CHFTMarketData::PrintMarketSummary()
{
    Print("=== MARKET SUMMARY ===");
    Print("Symbol: ", m_symbol);
    Print("Bid/Ask: ", m_analysis.bid, "/", m_analysis.ask);
    Print("Spread: ", m_analysis.spread);
    Print("State: ", GetMarketStateString());
    Print("Momentum: ", m_analysis.momentum);
    Print("Volatility: ", m_analysis.volatility);
    Print("Imbalance: ", m_analysis.imbalance);
}
