//+------------------------------------------------------------------+
//|                                             Compile_Test.mq5 |
//|                                    HFT Bot Compilation Test |
//+------------------------------------------------------------------+
#property copyright "HFT Professional"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property script_show_inputs

//--- Test compilation of all HFT components
#include "HFT_Config.mqh"
#include "Includes\HFT_RiskManager.mqh"
#include "Includes\HFT_MarketData.mqh"
#include "Includes\HFT_TradingEngine.mqh"
#include "Includes\HFT_Strategies.mqh"
#include "Includes\HFT_Performance.mqh"

//+------------------------------------------------------------------+
//| Script start function                                           |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== HFT COMPILATION TEST ===");
    
    // Test basic object creation
    CHFTConfig *config = new CHFTConfig();
    CHFTRiskManager *riskManager = new CHFTRiskManager();
    CHFTMarketData *marketData = new CHFTMarketData();
    CHFTTradingEngine *tradingEngine = new CHFTTradingEngine();
    CHFTStrategies *strategies = new CHFTStrategies();
    CHFTPerformance *performance = new CHFTPerformance();
    
    Print("✓ All objects created successfully");
    
    // Test basic initialization
    bool configOK = config.LoadConfig();
    bool riskOK = riskManager.Initialize(1.0, 5.0, 10.0, 5, 1.0);
    bool dataOK = marketData.Initialize(Symbol());
    bool engineOK = tradingEngine.Initialize(123456, "Test", 3.0);
    bool perfOK = performance.Initialize(true, true);
    
    SStrategyConfig stratConfig = {};
    stratConfig.enableArbitrage = true;
    stratConfig.enableMarketMaking = true;
    stratConfig.enableMomentum = true;
    stratConfig.enableScalping = true;
    stratConfig.spreadMultiplier = 1.5;
    stratConfig.minArbitrageSpread = 1.0;
    
    bool stratOK = strategies.Initialize(stratConfig);
    
    Print("✓ Configuration: ", configOK ? "OK" : "FAILED");
    Print("✓ Risk Manager: ", riskOK ? "OK" : "FAILED");
    Print("✓ Market Data: ", dataOK ? "OK" : "FAILED");
    Print("✓ Trading Engine: ", engineOK ? "OK" : "FAILED");
    Print("✓ Strategies: ", stratOK ? "OK" : "FAILED");
    Print("✓ Performance: ", perfOK ? "OK" : "FAILED");
    
    // Test basic functionality
    if(dataOK)
    {
        marketData.UpdateTick();
        SMarketAnalysis analysis = marketData.GetAnalysis();
        Print("✓ Market analysis - Bid: ", analysis.bid, " Ask: ", analysis.ask);
    }
    
    if(riskOK)
    {
        bool riskCheck = riskManager.CheckRiskLimits();
        Print("✓ Risk check: ", riskCheck ? "PASSED" : "FAILED");
    }
    
    // Clean up
    delete config;
    delete riskManager;
    delete marketData;
    delete tradingEngine;
    delete strategies;
    delete performance;
    
    Print("=== COMPILATION TEST COMPLETED ===");
    Print("All components compiled and initialized successfully!");
    Print("The HFT Bot is ready for use.");
}
